<template>
  <div class="product-detail-page">
    <!-- 产品详情页头部横幅 -->
    <ProductDetailHeaderBanner 
      :product-title="productTitle"
      :product-description="productDescription"
    />
    
    <!-- 产品功能展示 -->
    <ProductFeaturesSection 
      :product-title="productTitle"
    />
    
    <!-- 产品优势 -->
    <section class="section b3">
      <div class="section__inner">
        <h2>
          <span>生涯辅导系统 · 产品优势</span>
        </h2>
        <div class="product-advantage">
          <div class="advantage">
            <img src="@/assets/images/19fbff18682e560bcbfe251beeadfd70.svg" alt="科学测评、选科大数据分析" />
            <div class="title">
              <span>科学测评、选科大数据分析</span>
            </div>
            <div class="line"></div>
            <div class="desc">
              <span>通过科学的生涯测评让学生明晰自己的兴趣方向，提供海量资源供学生探索，选科大数据分析，助力科学决策。</span>
            </div>
          </div>
          <div class="advantage">
            <img src="@/assets/images/efbfa98ae06bcbd5d39cd66e1e025675.svg" alt="助力生涯教育提质增效" />
            <div class="title">
              <span>助力生涯教育提质增效</span>
            </div>
            <div class="line"></div>
            <div class="desc">
              <span>通过为教师提供丰富的生涯资源，为教师课程设计和日常教学提供便利，助力教师团队丰富生涯教育实践成果。</span>
            </div>
          </div>
          <div class="advantage">
            <img src="@/assets/images/img-solution-careerEducation-introduce.svg" alt="构建完善的生涯体系" />
            <div class="title">
              <span>构建完善的生涯体系</span>
            </div>
            <div class="line"></div>
            <div class="desc">
              <span>系统提供实时测评分析能力，按照年段与测评量表维度，查看学生测评结果的大数据分析报告。辅助各级实施整体统筹、精准管理与科学决策。</span>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 相关产品推荐 -->
<!--    <section class="section relative-recommendation-section">-->
<!--      <div class="section__inner">-->
<!--        <h2>-->
<!--          <span>相关产品服务推荐</span>-->
<!--        </h2>-->
<!--        <div class="relative-recommendation">-->
<!--          <router-link class="relative-recommendation__item" to="/solution/student-development-center">-->
<!--            <div class="top">-->
<!--              <img src="@/assets/images/solution-sdc-center.png" alt="学生发展指导中心建设相关解决方案">-->
<!--            </div>-->
<!--            <div class="bottom">-->
<!--              <div class="title">学生发展指导中心建设解决方案</div>-->
<!--              <div class="desc">基于丰富的学生发展指导中心建设经验，提供一站式解决方案，包含专用设备、空间规划、文化装饰等配套服务</div>-->
<!--            </div>-->
<!--            <div class="arrows"></div>-->
<!--          </router-link>-->
<!--          <router-link class="relative-recommendation__item" to="/solution/career-education">-->
<!--            <div class="top">-->
<!--              <img src="@/assets/images/solution-career-education.png" alt="职业生涯规划教育与选科指导相关解决方案">-->
<!--            </div>-->
<!--            <div class="bottom">-->
<!--              <div class="title">生涯教育与选科指导解决方案</div>-->
<!--              <div class="desc">助力学校实施职业生涯规划教育，包含云平台、空间建设、课程活动、师资培训，解决高中生涯规划教育难题</div>-->
<!--            </div>-->
<!--            <div class="arrows"></div>-->
<!--          </router-link>-->
<!--        </div>-->
<!--      </div>-->
<!--    </section>-->

  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { computed } from 'vue'
import ProductDetailHeaderBanner from '@/components/product/ProductDetailHeaderBanner.vue'
import ProductFeaturesSection from '@/components/product/ProductFeaturesSection.vue'

const route = useRoute()
const productId = route.params.id

// 根据产品ID获取产品信息
const getProductInfo = (id) => {
  const products = {
    '1': {
      title: '生涯测评系统',
      description: '专业的生涯兴趣、能力、价值观测评，帮助学生了解自我，制定科学的生涯规划。'
    },
    '2': {
      title: '选科指导系统',
      description: '基于测评结果，为学生提供个性化的选科指导，助力学生做出明智的学科选择。'
    },
    '3': {
      title: '生涯指导资源系统',
      description: '丰富的生涯教育资源库，支持课程管理和教学实施，为教师提供全面的教学支持。'
    },
    '4': {
      title: '学生生涯档案系统',
      description: '完整的学生生涯档案管理，记录成长轨迹和发展历程，支持个性化指导。'
    },
    '5': {
      title: '学校生涯教育管理系统',
      description: '全面的学校生涯教育管理平台，支持数据分析和决策，提升管理效率。'
    },
    '6': {
      title: '生涯教育课程教学系统',
      description: '专业的生涯教育课程教学平台，支持多种教学模式，提升教学效果。'
    },
    'device-1': {
      title: 'AI生涯指导舱',
      description: '智能化的生涯指导设备，提供沉浸式的生涯体验和指导服务。'
    },
    'device-2': {
      title: '学生发展数字大屏',
      description: '大型数字展示设备，用于展示学生发展数据和成果。'
    }
  }
  
  return products[id] || { title: '未知产品', description: '产品信息暂无' }
}

const productInfo = computed(() => getProductInfo(productId))
const productTitle = computed(() => productInfo.value.title)
const productDescription = computed(() => productInfo.value.description)
</script>

<style scoped>
.product-detail-page {
  min-height: 100vh;
}

/* 产品优势样式 */
.section.b3 {
  background: #f8f9fa;
  padding: 80px 0;
}

.section.b3 h2 {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: #222;
  margin-bottom: 60px;
}

.product-advantage {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.advantage {
  text-align: center;
  padding: 48px 24px 40px 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.advantage img {
  width: 80px;
  height: 80px;
  margin-bottom: 32px;
}

.advantage .title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  margin-bottom: 18px;
}

.advantage .line {
  width: 56px;
  height: 4px;
  background: #ffe200;
  margin: 0 auto 24px auto;
  border-radius: 2px;
}

.advantage .desc {
  font-size: 16px;
  color: #666;
  line-height: 1.7;
  margin-top: 0;
}

@media (max-width: 1000px) {
  .product-advantage {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  .advantage {
    padding: 32px 12px 28px 12px;
  }
  .advantage img {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
  }
  .advantage .title {
    font-size: 18px;
  }
}

/* 相关产品推荐样式 */
.relative-recommendation-section {
  padding: 80px 0;
}

.relative-recommendation-section h2 {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 60px;
}

.relative-recommendation {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.relative-recommendation__item {
  display: block;
  text-decoration: none;
  color: inherit;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.relative-recommendation__item:hover {
  transform: translateY(-5px);
}

.relative-recommendation__item .top img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.relative-recommendation__item .bottom {
  padding: 20px;
}

.relative-recommendation__item .title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.relative-recommendation__item .desc {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

/* 联系我们样式 */
.section.b9 {
  background: linear-gradient(135deg, #19BEF5, #334CD5);
  color: white;
  text-align: center;
  padding: 80px 0;
}

.section.b9 h2 {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 40px;
}

.section.b9 .button {
  display: inline-block;
  padding: 15px 40px;
  background: white;
  color: #19BEF5;
  text-decoration: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.section.b9 .button:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-advantage {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .relative-recommendation {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .section.b3 h2,
  .relative-recommendation-section h2,
  .section.b9 h2 {
    font-size: 24px;
  }
}
</style>
