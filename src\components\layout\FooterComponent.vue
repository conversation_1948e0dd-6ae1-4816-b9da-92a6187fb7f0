<script setup>
import { ref } from 'vue'

// 获取当前年份
const currentYear = ref(new Date().getFullYear())
</script>

<template>
  <footer class="footer">
    <div class="footer__inner container">
      <div class="footer__content flex">
        <div class="footer__column">
          <div class="footer__title">核心产品 PRODUCTS</div>
          <div class="footer__links">
            <p>生涯辅导系统</p>
            <p>心理健康系统</p>
            <p>学业评估系统</p>
            <p>AI生涯指导舱</p>
            <p>生涯教室专用设备</p>
            <p>学科职业体验课程</p>
            <p>生涯探索游园会</p>
          </div>
        </div>
        
        <div class="footer__column">
          <div class="footer__title">解决方案 SOLUTIONS</div>
          <div class="footer__links">
            <p>高中智能生涯教室整体解决方案</p>
            <p>生涯教育与选科指导解决方案</p>
            <p>心理健康解决方案</p>
            <p>学业指导解决方案</p>
            <p>学生发展指导中心建设解决方案</p>
          </div>
        </div>
        
        <div class="footer__column">
          <div class="footer__title">联系我们 CONTACT US</div>
          <div class="footer__contact">
            <div class="footer__contact-info">
              <p>电话/微信：159 0100 6750 （9:00-18:00）</p>
              <p>邮箱：<EMAIL></p>
              <p>地址：北京市朝阳区中国五矿大厦</p>
              <p><span class="invisible">地址：</span>厦门市思明区新景中心</p>
              <p><span class="invisible">地址：</span>深圳市福田区上步信托大楼</p>
              <div class="footer__qrcode-tip">了解最新活动与资讯，关注试界教育公众号</div>
            </div>
            <div class="footer__qrcode">
              <img src="@/assets/images/qrcode.jpg" alt="试界教育公众号" />
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer__copyright">
        <div class="copyright-text">
          <span>Copyright © 2012 - {{ currentYear }}</span>
          <span class="divider"></span>
          <span>试界教育科技（北京）有限公司</span>
          <span class="divider"></span>
          <router-link to="/sitemap">网站地图</router-link>
          <span class="divider"></span>
          <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer">ICP备案号：京ICP备17021853号-3</a>
          <span class="divider"></span>
          <a href="https://beian.mps.gov.cn/#/query/webSearch" target="_blank" rel="noopener noreferrer">公安备案号：京公网安备11010502056263号</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.footer {
  background-color: #130472;
  color: #fff;
  padding: 60px 0 20px;
}

.footer__inner {
  display: flex;
  flex-direction: column;
}

.footer__content {
  justify-content: space-between;
}

.footer__column {
  flex: 1;
  margin-right: 30px;
}

.footer__column:last-child {
  margin-right: 0;
}

.footer__title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer__title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: #334CD5;
}

.footer__links p {
  margin-bottom: 10px;
  font-size: 14px;
  color: #ccc;
}

.footer__contact {
  display: flex;
}

.footer__contact-info {
  flex: 1;
}

.footer__contact-info p {
  margin-bottom: 10px;
  font-size: 14px;
  color: #ccc;
}

.invisible {
  visibility: hidden;
}

.footer__qrcode {
  width: 100px;
  height: 100px;
}

.footer__qrcode img {
  width: 100%;
  height: 100%;
}

.footer__qrcode-tip {
  margin-top: 20px;
  font-size: 14px;
  color: #ccc;
}

.footer__copyright {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
  color: #999;
}

.copyright-text {
  text-align: center;
}

.divider {
  display: inline-block;
  margin: 0 10px;
}

a {
  color: #999;
}

a:hover {
  color: #334CD5;
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .footer__content {
    flex-direction: column;
  }
  
  .footer__column {
    margin-right: 0;
    margin-bottom: 30px;
  }
  
  .footer__copyright {
    flex-direction: column;
  }
  
  .copyright-text {
    flex-direction: column;
    align-items: center;
  }
  
  .divider {
    display: none;
  }
  
  .copyright-text span, 
  .copyright-text a {
    margin-bottom: 5px;
  }
}
</style> 