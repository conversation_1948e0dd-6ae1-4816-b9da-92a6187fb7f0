<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Import images
import logoQinghuafu from '@/assets/images/logo-qinghuafu.png'
import solutionShengyajiaoyu from '@/assets/images/solution-shengyajiaoyu.png'
import submenuIcon from '@/assets/images/submenu-icon.png'
import xinliImage from '@/assets/images/solution-xinlijiankang.png'
import xueyeImage from '@/assets/images/solution-xueyezhidao.png'
import fazhanImage from '@/assets/images/solution-xueshengfazhan.png'
// 案例数据
const cases = ref([
  {
    id: 136,
    title: '清华大学附属中学',
    desc: '试界教育助力清华附中共构个性化生涯规划教育模式，搭建以素养提升为中心的"生命设计"培养方案<br>学校生涯发展指导办公室邀请试界教育共构学生职业生涯规划教育培养体系',
    logo: logoQinghuafu,
    bg: solutionShengyajiaoyu
  },
  {
    id: 137,
    title: '中国人民大学附属中学',
    desc: '通过学科职业体验课，满足学生发展指导个性化需求，丰富职业生涯规划教育体系<br>',
    logo: logoQinghuafu,
    bg: xinliImage
  },
  {
    id: 138,
    title: '深圳中学',
    desc: '学生生涯规划探索活动与教师培训结合，师生共育，助力生涯启航',
    logo: logoQinghuafu,
    bg: xueyeImage
  },
  {
    id: 139,
    title: '厦门双十中学',
    desc: '构建学生发展指导体系，落成学生发展指导中心，培养德才兼备的创新型领军预备人才',
    logo: logoQinghuafu,
    bg: fazhanImage
  }
])

// 客户故事
const clientStories = ref([
  {
    id: 2,
    title: '客户故事·清华大学附属中学',
    content: '试界教育助力清华附中<br>共构个性化生涯教育模式',
    quote: {
      name: '王美荣',
      title: '清华附中原学生生涯指导办公室教师',
      content: '基于清华附中的创新型人才培养理念，试界教育与学校共构"生命设计"体系，围绕学生的个人生涯发展需求和素养提升打造生涯系列课程，课程效果得到了师生的一致认可。'
    }
  },
  {
    id: 5,
    title: '客户故事·双十中学',
    content: '试界携手双十中学一起向未来，<br>全面建设与落实学生发展指导体系',
    quote: {
      name: '欧阳玲',
      title: '福建省厦门双十中学校长',
      content: '通过试界教育的中学生涯教育解决方案，我们在生涯测评的施测、学职体验课程的开设、校本教材的定制等方面'
    }
  }
])

// 当前显示的客户故事索引
const currentStoryIndex = ref(0)

// 跳转到案例详情页
const goToCaseDetail = (id) => {
  router.push(`/case/${id}`)
}

// 跳转到所有案例页面
const goToAllCases = () => {
  router.push('/case')
}

// 切换客户故事
const changeStory = (index) => {
  currentStoryIndex.value = index
}
</script>

<template>
  <section class="case-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">各个高校学生共同信赖的选择</h2>
        <div class="view-more" @click="goToAllCases">
          <span>查看全部案例</span>
          <i class="arrow-right">→</i>
        </div>
      </div>

      <div class="cases-grid">
        <div v-for="item in cases" :key="item.id"
             class="case-card"
             @click="goToCaseDetail(item.id)">
          <div class="case-top" :style="{ backgroundImage: `url(${item.bg})` }">
            <img :src="item.logo" :alt="item.title" class="case-logo">
          </div>
          <div class="case-bottom">
            <div class="case-title">{{ item.title }}</div>
            <div class="case-desc" v-html="item.desc"></div>
          </div>
          <div class="arrow-button">
            <span class="arrow-icon">→</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.case-section {
  padding: 80px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.view-more {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #334CD5;
  font-size: 16px;
}

.arrow-right {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  font-size: 18px;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 60px 0;
}

.case-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}
.case-card:hover .case-bottom {
  background-color: #130472;
  height: 100%;
  bottom: 0;
  top: auto;
}
.case-card:hover .case-title {
  color: white;
  font-size: 24px;
}
.case-card:hover .case-desc {
  color: white;
}
.case-card:hover .arrow-button {
  background-color: #FFCC00;
  transform: translateY(-2px);
}
.case-top {
  height: 200px;
  background-size: cover;
  background-position: center;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.case-logo {
  height: 60px;
  max-width: 80%;
  z-index: 2;
  position: relative;
}

.case-bottom {
  padding: 20px;
  padding-bottom: 40px;
  position: relative;
  transition: all 0.3s;
}

.case-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  transition: all 0.3s;
}

.case-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.case-arrow {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.case-arrow img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.arrow-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  background-color: #00BCD4;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: bold;
  z-index: 10;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 188, 212, 0.3);
}



.client-stories {
  margin-bottom: 80px;
  position: relative;
}

.client-story {
  display: flex;
  gap: 30px;
}

.client-quote {
  flex: 1;
  background-color: #334CD5;
  color: #fff;
  border-radius: 8px;
  padding: 40px;
  position: relative;
}

.client-quote::before {
  content: '"';
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 80px;
  opacity: 0.2;
}

.quote-title {
  font-size: 18px;
  margin-bottom: 20px;
}

.quote-title span:first-child {
  font-weight: normal;
}

.quote-title span:last-child {
  font-weight: bold;
}

.quote-content {
  font-size: 18px;
  line-height: 1.8;
  font-style: italic;
}

.client-story-card {
  width: 300px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 30px;
  cursor: pointer;
  transition: all 0.3s;
}

.client-story-card:hover {
  background-color: #eef1ff;
}

.story-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
}

.story-content {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20px;
}

.story-link {
  width: 30px;
  height: 30px;
  background-size: contain;
  background-repeat: no-repeat;
}

.story-dots {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ddd;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s;
}

.dot.active {
  background-color: #334CD5;
}

.school-partners {
  text-align: center;
}

.partners-showcase {
  margin-top: 40px;
  height: 300px;
  background-color: #334CD5;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.partners-image {
  width: 80%;
  height: 200px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.case-card:hover .case-arrow img {
  filter: brightness(0) invert(1);
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .cases-grid {
    grid-template-columns: 1fr;
  }
  
  .client-story {
    flex-direction: column;
  }
  
  .client-story-card {
    width: 100%;
  }
}
</style> 