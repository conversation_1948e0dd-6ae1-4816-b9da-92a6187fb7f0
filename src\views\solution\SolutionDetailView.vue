<template>
  <div class="solution-detail-page">
    <!-- 详情页头部横幅 -->
    <DetailHeaderBanner :solution="currentSolution" />
    
    <!-- 方案背景 -->
    <SolutionBackground />
    
    <!-- 方案介绍 -->
    <SolutionIntroduction />
    

  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import DetailHeaderBanner from '@/components/solution/DetailHeaderBanner.vue'
import SolutionBackground from '@/components/solution/SolutionBackground.vue'
import SolutionIntroduction from '@/components/solution/SolutionIntroduction.vue'

const route = useRoute()

// 解决方案数据
const solutions = ref({
  'career-education': {
    id: 'career-education',
    title: '生涯规划教育与选科指导解决方案',
    subtitle: '生涯教育与选科指导解决方案',
    description: '解决新高考下职业生涯教育与选科指导相关问题，为中学提供体系化整体解决方案。产品服务包含云平台软件、专用硬件设备、课程活动、教师培训，可以灵活根据学校需求进行定制。',
    breadcrumb: '生涯教育与选科指导解决方案'
  },
  'career-classroom': {
    id: 'career-classroom',
    title: '高中智能生涯教室整体解决方案',
    subtitle: '高中智能生涯教室解决方案',
    description: '试界教育基于大量学生发展指导中心的建设经验，结合领先的学生发展指导理念与学校特色，提供学生发展指导中心理念顶层设计、文化设计、装修改造等一站式解决方案。',
    breadcrumb: '高中智能生涯教室整体解决方案'
  },
  'mental-health': {
    id: 'mental-health',
    title: '心理健康解决方案',
    subtitle: '心理健康解决方案',
    description: '试界教育针对区域、学校定制心理健康解决方案，服务涵盖心理测评、心理课堂，赋能学生发展指导',
    breadcrumb: '心理健康解决方案'
  },
  'academic-advising': {
    id: 'academic-advising',
    title: '学业指导解决方案',
    subtitle: '学业指导解决方案',
    description: '试界自主研发学习能力测评系统与体系化指导微课，助力中学学业指导工作，培育终生学习能力',
    breadcrumb: '学业指导解决方案'
  },
  'student-development-center': {
    id: 'student-development-center',
    title: '学生发展指导中心建设解决方案',
    subtitle: '学生发展指导中心建设解决方案',
    description: '基于丰富的学生发展指导中心建设经验，提供学生发展指导专用设备、文化装饰一站式解决方案',
    breadcrumb: '学生发展指导中心建设解决方案'
  }
})

// 当前解决方案
const currentSolution = computed(() => {
  const solutionId = route.params.id
  return solutions.value[solutionId] || solutions.value['career-education']
})

onMounted(() => {
  // 页面加载时滚动到顶部
  window.scrollTo(0, 0)
})
</script>

<style scoped>
.solution-detail-page {
  min-height: 100vh;
}
</style>