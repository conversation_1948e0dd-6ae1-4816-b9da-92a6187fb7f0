<template>
  <div>
    <!-- 职业生涯规划教育 -->
    <section class="section section-1">
      <div class="section__inner">
        <h2 class="main-title">职业生涯规划教育</h2>
        
        <!-- 生涯辅导云平台 -->
        <div class="subtitle">
          <span>生涯辅导云平台</span>
        </div>
        
                 <div class="grid-container">
           <router-link 
             v-for="product in careerCloudProducts" 
             :key="product.id" 
             :to="`/product/${product.id}`" 
             class="product__item"
             @click="handleProductClick(product)"
           >
             <div class="product-image">
               <img :src="getProductImage(product.image)" :alt="product.title" />
             </div>
             <div class="product-content">
               <div class="product-title">{{ product.title }}</div>
               <div class="arrow-icon">→</div>
             </div>
           </router-link>
         </div>
      </div>
    </section>

    <!-- 生涯空间专用设备 -->
    <section class="section section-2">
      <div class="section__inner">
        <h2 class="main-title">生涯空间专用设备</h2>
        
                 <div class="device-container">
           <router-link :to="`/product/device-${deviceProducts[0].id}`" class="device-item" @click="handleDeviceClick(deviceProducts[0])">
             <div class="device-image">
               <img :src="getProductImage(deviceProducts[0].image)" :alt="deviceProducts[0].title" />
             </div>
             <div class="device-content">
               <div class="device-title">{{ deviceProducts[0].title }}</div>
               <div class="arrow-icon">→</div>
             </div>
           </router-link>
           
           <router-link :to="`/product/device-${deviceProducts[1].id}`" class="device-item" @click="handleDeviceClick(deviceProducts[1])">
             <div class="device-image">
               <img :src="getProductImage(deviceProducts[1].image)" :alt="deviceProducts[1].title" />
             </div>
             <div class="device-content">
               <div class="device-title">{{ deviceProducts[1].title }}</div>
               <div class="arrow-icon">→</div>
             </div>
           </router-link>
         </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 生涯辅导云平台产品 - 按照图片中的6个系统
const careerCloudProducts = ref([
  {
    id: 1,
    title: '生涯测评系统',
    image: 'solution-shengyajiaoyu.png', // 使用现有图片
    alt: '生涯测评系统'
  },
  {
    id: 2,
    title: '选科指导系统',
    image: 'solution-xueyezhidao.png', // 使用现有图片
    alt: '选科指导系统'
  },
  {
    id: 3,
    title: '生涯指导资源系统',
    image: 'solution-academic-advising.png', // 使用现有图片
    alt: '生涯指导资源系统'
  },
  {
    id: 4,
    title: '学生生涯档案系统',
    image: 'solution-xueshengfazhan.png', // 使用现有图片
    alt: '学生生涯档案系统'
  },
  {
    id: 5,
    title: '学校生涯教育管理系统',
    image: 'solution-sdc-center.png', // 使用现有图片
    alt: '学校生涯教育管理系统'
  },
  {
    id: 6,
    title: '生涯教育课程教学系统',
    image: 'solution-career-classroom.png', // 使用现有图片
    alt: '生涯教育课程教学系统'
  }
])

// 生涯空间专用设备产品
const deviceProducts = ref([
  {
    id: 1,
    title: 'AI生涯指导舱',
    image: 'solution-xinlijiankang.png', // 使用现有图片作为指导舱
    alt: 'AI生涯指导舱'
  },
  {
    id: 2,
    title: '学生发展数字大屏',
    image: 'img-overall.png', // 使用现有图片作为数字大屏
    alt: '学生发展数字大屏'
  }
])

// 获取产品图片
const getProductImage = (imageName) => {
  try {
    return new URL(`../../assets/images/${imageName}`, import.meta.url).href
  } catch (error) {
    console.warn(`Product image not found: ${imageName}`)
    return ''
  }
}

// 处理产品点击事件
const handleProductClick = (product) => {
  console.log('产品被点击:', product)
}

// 处理设备点击事件
const handleDeviceClick = (device) => {
  console.log('设备被点击:', device)
}
</script>

<style scoped>
.section {
  padding: 80px 0;
}

.section-1 {
  background-color: white;
}

.section-2 {
  background-color: #f9f9f9;
}

.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.subtitle {
  text-align: center;
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
}

/* 职业生涯规划教育网格布局 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 20px;
}

.product__item {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: inherit;
  position: relative;
  z-index: 1;
}

.product__item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background-color: #1e40af;
}

.product__item:hover .product-title {
  color: white;
}

.product__item:hover .arrow-icon {
  background-color: #fde047;
  color: #1e40af;
}

.product-image {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product__item:hover .product-image img {
  transform: scale(1.05);
}

.product-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.product-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.arrow-icon {
  width: 40px;
  height: 40px;
  background-color: #00a4ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: bold;
  flex-shrink: 0;
}

/* 生涯空间专用设备布局 */
.device-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 40px;
}

.device-item {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: inherit;
  position: relative;
  z-index: 1;
}

.device-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background-color: #1e40af;
}

.device-item:hover .device-title {
  color: white;
}

.device-item:hover .arrow-icon {
  background-color: #fde047;
  color: #1e40af;
}

.device-image {
  position: relative;
  overflow: hidden;
  height: 300px;
}

.device-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.device-item:hover .device-image img {
  transform: scale(1.05);
}

.device-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.device-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  flex: 1;
}

/* 响应式设计 */
@media screen and (max-width: 960px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, auto);
  }
  
  .device-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .section {
    padding: 60px 0;
  }
  
  .main-title {
    font-size: 28px;
  }
}

@media screen and (max-width: 768px) {
  .section__inner {
    padding: 0 16px;
  }
  
  .grid-container {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, auto);
    gap: 20px;
  }
  
  .main-title {
    font-size: 24px;
  }
  
  .subtitle {
    font-size: 16px;
  }
  
  .device-image {
    height: 250px;
  }
}

@media screen and (max-width: 480px) {
  .section {
    padding: 40px 0;
  }
  
  .main-title {
    font-size: 20px;
  }
  
  .subtitle {
    font-size: 14px;
  }
  
  .device-image {
    height: 200px;
  }
}
</style>