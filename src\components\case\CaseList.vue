<template>
  <section class="case-list-section">
    <section class="section bb1">
      <div class="section__inner">
        <h2><span>精品学校案例</span></h2>
      </div>
    </section>
    <section class="section b1">
      <div class="section__inner">
        <div class="case-container">
          <div v-for="caseItem in caseList" :key="caseItem.id" class="case">
            <div class="top">
              <div class="img" :style="{ backgroundImage: `url(${caseItem.image})` }"></div>
              <div class="school">{{ caseItem.school }}</div>
              <h3 class="title">{{ caseItem.title }}</h3>
            </div>
            <div class="bottom">
              <div class="desc">{{ caseItem.description }}</div>
              <router-link :to="`/case/${caseItem.id}`" class="btn">
                <span>详细了解</span>
                <svg class="icon" viewBox="0 0 1024 1024" width="20" height="20">
                  <path d="M885.113 489.373L628.338 232.599c-12.496-12.497-32.758-12.497-45.254 0-12.497 12.497-12.497 32.758 0 45.255l203.3 203.3H158.025c-17.036 0-30.846 13.811-30.846 30.846 0 17.036 13.811 30.846 30.846 30.846h628.36L583.084 746.147c-12.497 12.496-12.497 32.758 0 45.255 6.248 6.248 14.438 9.372 22.627 9.372s16.379-3.124 22.627-9.372l256.775-256.775a31.999 31.999 0 0 0 0-45.254z" fill="#334CD5"/>
                </svg>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const caseList = ref([
  {
    id: 1,
    school: '北京大学附属中学',
    title: '试界教育助力北大附中，创新生涯课程建设',
    description: '北大附中借助试界教育的课程体系和师资力量，开设了PBL学科职业体验课，帮助学生深度体验不同的学科职业，助力学生选科、选专业。',
    image: new URL('@/assets/images/solution-shengyajiaoyu.png', import.meta.url).href
  },
  {
    id: 2,
    school: '人大附中石景山学校',
    title: '试界助力人大附中石景山学校，高效推进心理健康工作，打造区域特色标杆',
    description: '人大附中石景山学校携手试界教育，共同搭建心理健康云平台，加强心理健康工作过程管理，健全筛查预警机制，提升日常咨询辅导水平。',
    image: new URL('@/assets/images/solution-xinlijiankang.png', import.meta.url).href
  },
  {
    id: 3,
    school: '福建省厦门集美中学',
    title: '试界教育携手集美中学构建"精准洞察·科学施教"学业指导综合解决方案',
    description: '基于集美中学"知诚毅·悟嘉庚"学生远航发展指导体系的核心精神以及"15331+N"工作模式，试界教育的段博士带领团队定制青少年学习力提升综合解决方案。',
    image: new URL('@/assets/images/solution-xueyezhidao.png', import.meta.url).href
  },
  {
    id: 4,
    school: '中国人民大学附属中学',
    title: '校内外资源联动，完善人大附中「科技+体验」的生涯规划教育体系',
    description: '试界教育为中国人民大学附属中学提供课程体系与师资队伍，建设体验式的专业职业探索课程，以高效且结构化的形式帮助学生积极探索、自主体验。',
    image: new URL('@/assets/images/solution-sdc-center.png', import.meta.url).href
  },
  {
    id: 5,
    school: '清华大学附属中学',
    title: '推进高水平师资队伍建设，搭建从生涯唤醒到学业提升的学生发展体系',
    description: '试界教育为清华大学附属中学提供生涯教育解决方案，助力学校建设完整的生涯教育体系，从唤醒、探索、决策到行动阶段提供一站式的内容与技术支持。',
    image: new URL('@/assets/images/solution-academic-advising.png', import.meta.url).href
  },
  {
    id: 6,
    school: '深圳中学',
    title: '"适应·成长·创新"试界赋能深圳中学生涯教育解决方案',
    description: '试界教育结合深圳中学"以学生为中心"的教育理念，为深圳中学策划生涯探索系列活动。活动入选2020年广东省普通高中生涯教育优秀案例名单。',
    image: new URL('@/assets/images/solution-career-classroom.png', import.meta.url).href
  }
])
</script>

<style scoped>
.case-list-section {
  background: #F7F7F7;
}
.section {
  padding: 30px 0;
}
.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.bb1 {
  background: #F7F7F7;
}
.bb1 h2 {
  line-height: 30px;
  margin: 0;
  font-size: 28px;
  font-family: PingFangSC, Microsoft Yahei, sans-serif;
  font-weight: bold;
  color: #000000;
  text-align: center;
}
.b1 {
  background: #F7F7F7;
}
.case-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 40px;
}
.case {
  width: calc(50% - 20px);
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.case:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
.case .top {
  height: 242px;
  position: relative;
}
.case .top .img {
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.case .top .school {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 16px;
  line-height: 24px;
  color: #fff;
  font-weight: bold;
  padding: 10px 20px;
  background: #19BEF5;
}
.case .top .title {
  position: absolute;
  bottom: 24px;
  left: 20px;
  font-size: 24px;
  line-height: 36px;
  color: #fff;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}
.case .bottom {
  padding: 16px 20px 28px;
  position: relative;
}
.case .bottom .desc {
  font-size: 16px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 24px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.case .bottom .btn {
  cursor: pointer;
  width: 136px;
  height: 38px;
  border: 2px solid #334CD5;
  color: #334CD5;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  box-sizing: border-box;
  text-decoration: none;
  transition: all 0.2s;
}
.case .bottom .btn:hover {
  background-color: #19BEF5;
  border: 2px solid #19BEF5;
  color: #fff;
}
.case .bottom .btn:hover svg path {
  fill: #fff;
}
@media (max-width: 960px) {
  .case-container {
    flex-direction: column;
  }
  .case {
    width: 100%;
  }
}
@media (max-width: 720px) {
  .section {
    padding: 20px 0;
  }
  .bb1 h2 {
    line-height: 25px;
    font-size: 24px;
  }
  .case .top {
    height: 200px;
  }
  .case .top .title {
    font-size: 20px;
    line-height: 28px;
  }
}
@media (max-width: 540px) {
  .section__inner {
    padding: 0 16px;
  }
  .bb1 h2 {
    line-height: 20px;
    font-size: 20px;
  }
  .case .top {
    height: 180px;
  }
  .case .top .title {
    font-size: 18px;
    line-height: 24px;
  }
  .case .bottom .desc {
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
