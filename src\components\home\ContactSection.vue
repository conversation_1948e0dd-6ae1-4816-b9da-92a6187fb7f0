<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 跳转到产品咨询页面
const goToConsultation = () => {
  router.push('/contact-us/product-consultation')
}
</script>

<template>
<!--  <section class="contact-section">-->
<!--    <div class="container">-->
<!--      <h2 class="section-title">-->
<!--        <span>现在开始</span>-->
<!--        <br>-->
<!--        <span>轻松部署生涯规划教育与学生发展指导体系</span>-->
<!--      </h2>-->
<!--      <button class="consultation-btn" @click="goToConsultation">产品咨询</button>-->
<!--    </div>-->
<!--  </section>-->
</template>

<style scoped>
.contact-section {
  padding: 100px 0;
  background-size: cover;
  background-position: center;
  position: relative;
  text-align: center;
  color: #fff;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.container {
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 40px;
}

.consultation-btn {
  background-color: #334CD5;
  color: #fff;
  border: none;
  padding: 15px 40px;
  font-size: 18px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.consultation-btn:hover {
  background-color: #2a3eb8;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .section-title {
    font-size: 28px;
  }
}
</style> 