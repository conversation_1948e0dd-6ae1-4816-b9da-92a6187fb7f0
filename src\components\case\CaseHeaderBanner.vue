<template>
  <section class="case-header-banner">
    <div class="section__inner">
      <div class="breadcrumb">
        <div class="breadcrumb__inner">
          <span>
            <router-link class="breadcrumb__item" to="/">
              <span>首页</span>
            </router-link>
          </span>
          <span class="breadcrumb__seperate">/</span>
          <span class="breadcrumb__item">
            <span>喜报案例</span>
          </span>
        </div>
      </div>
      <h1 class="title">
        <span>中学信赖的职业生涯规划教育、学生发展指导共构伙伴</span>
      </h1>
      <div class="intro">
        <span>促进高中育人方式改革，赋能中学学生发展指导</span>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>

<style scoped>
.case-header-banner {
  background-image: url('@/assets/images/bg-solution-header.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 140px 0 180px;
  position: relative;
  color: white;
  min-height: 500px;
}
.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.breadcrumb {
  margin-bottom: 40px;
}
.breadcrumb__inner {
  display: flex;
  align-items: center;
  gap: 8px;
}
.breadcrumb__item {
  color: white;
  text-decoration: none;
  font-size: 14px;
}
.breadcrumb__item:hover {
  color: #19BEF5;
}
.breadcrumb__seperate {
  color: white;
  font-size: 14px;
}
.title {
  font-size: 36px;
  line-height: 54px;
  margin: 0 0 16px 0;
  color: #FFFFFF;
  font-weight: bold;
  text-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
}
.intro {
  font-size: 16px;
  line-height: 24px;
  color: #FFFFFF;
  margin: 0;
}
@media (max-width: 720px) {
  .title {
    font-size: 28px;
    line-height: 42px;
  }
}
@media (max-width: 540px) {
  .title {
    font-size: 24px;
    line-height: 36px;
  }
  .intro {
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
