<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goToConsultation = () => {
  router.push('/contact-us/product-consultation')
}
</script>

<template>
  <section class="banner-section">
    <div class="banner-content">
      <div class="container">
        <div class="banner-text">
          <h2 class="banner-title">笔墨屋</h2>
          <h1 class="banner-subtitle">大学生职业生涯规划教育与发展指导解决方案供应商</h1>
          <div class="banner-intro">
            <p>
              北京笔墨屋科技有限责任公司，2015年成立，是一家特色鲜明的科技型企业。专注软件开发、技术服务、IT 人才生态三位一体。
            </p>
            <p>
              在软件开发领域，我司凭借专业的技术团队与先进的开发理念，持续创新，开发出一系列契合市场需求的软件产品。在技术服务方面，为客户提供全面且多层次的技术支持，确保客户在使用软件过程中所遇问题能够得到及时有效的解决。在 IT 人才生态构建方面，我司致力于打造一个涵盖人才培养、输送和发展的完善生态系统，为行业输送优秀的 IT 人才。
            </p>
          </div>
          <button class="consultation-btn" @click="goToConsultation">产品咨询</button>
        </div>
      </div>
    </div>
    <div class="contact-float">
      <div class="contact-icon">
        <span>电话咨询</span>
      </div>
    </div>
    <div class="wechat-float">
      <div class="wechat-icon">
        <span>微信咨询</span>
      </div>
    </div>
  </section>
</template>

<style scoped>
.banner-section {
  background-image: url('@/assets/images/banner-bg.jpg');
  background-size: cover;
  background-position: center;
  height: 600px;
  display: flex;
  align-items: center;
  color: #fff;
  position: relative;
  margin-top: 100px; /* 40px顶部栏 + 60px导航栏 */
}

.banner-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.banner-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding-left: 30px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.banner-text {
  max-width: 700px;
  text-align: left;
}

.banner-title {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 10px;
}

.banner-subtitle {
  font-size: 28px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 30px;
}

.banner-intro {
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 30px;
}

.banner-intro p {
  margin-bottom: 15px;
}

.consultation-btn {
  background-color: #FFE200;
  color: #000;
  border: none;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.consultation-btn:hover {
  background-color: #FFD700;
}

.contact-float {
  position: fixed;
  right: 0;
  top: 50%;
  z-index: 100;
  transform: translateY(-50%);
}

.contact-icon {
  background-color: #00a4ff;
  color: white;
  padding: 15px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.wechat-float {
  position: fixed;
  right: 0;
  top: calc(50% + 60px);
  z-index: 100;
}

.wechat-icon {
  background-color: #00a4ff;
  color: white;
  padding: 15px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .banner-section {
    height: auto;
    padding: 100px 0;
  }
  
  .banner-title {
    font-size: 30px;
  }
  
  .banner-subtitle {
    font-size: 22px;
  }
}
</style> 