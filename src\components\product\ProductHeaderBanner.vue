<template>
  <section class="section header-banner">
    <div class="section__inner">
      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb__inner">
          <router-link to="/" class="breadcrumb__item">
            <span>首页</span>
          </router-link>
          <span class="breadcrumb__seperate">/</span>
          <span class="breadcrumb__item">
            <span>服务项目</span>
          </span>
        </div>
      </div>
      
      <!-- 主标题 -->
      <h1 class="title1">
        <span>适配中学学生发展指导场景</span>
      </h1>
      
      <!-- 副标题 -->
      <div class="intro">
        <span>核心产品涵盖职业生涯规划、心理健康、学业指导</span>
      </div>
    </div>
  </section>
</template>

<script setup>
// 无需额外逻辑
</script>

<style scoped>
.header-banner {
  background-image: url('@/assets/images/product-header-banner-465408453888f401cbe4d4745a4f29a2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 140px 0 180px;
  position: relative;
  color: white;
  min-height: 500px;
}

.header-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.section__inner {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 面包屑样式 */
.breadcrumb {
  margin-bottom: 40px;
}

.breadcrumb__inner {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb__item {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb__item:hover {
  color: white;
}

.breadcrumb__seperate {
  margin: 0 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* 标题样式 */
.title1 {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.intro {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .header-banner {
    padding: 100px 0 120px;
    min-height: 400px;
  }
  
  .section__inner {
    padding: 0 16px;
  }
  
  .title1 {
    font-size: 32px;
  }
  
  .intro {
    font-size: 16px;
  }
  
  .breadcrumb {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 480px) {
  .title1 {
    font-size: 28px;
  }
  
  .intro {
    font-size: 14px;
  }
}
</style>