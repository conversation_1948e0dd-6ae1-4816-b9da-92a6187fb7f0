<template>
  <div>
    <!-- 方案背景标题 -->
    <section class="section title-section">
      <div class="section__inner">
        <h2 class="section-title">
          <span>生涯规划教育 · 方案背景</span>
        </h2>
      </div>
    </section>

    <!-- 第一个背景点 - 浅灰色背景 -->
    <section class="section background-section-1">
      <div class="section__inner">
        <div class="solution-point">
          <div class="left">
            <div class="title-with-line">
              <span class="title">新高考背景下生涯规划教育成为趋势</span>
              <div class="title-underline"></div>
            </div>
            <div class="block">Policy Context</div>
          </div>
          <div class="right">
            <div class="content-text">
              <p>2019年，《国务院办公厅关于新时代推进普通高中育人方式改革的指导意见》，提高学生在选修课程、选考科目、报考专业和未来发展方向等方面的自主选择能力。</p>
              <p>2022年，《教育部关于做好2022年普通高校招生工作的通知》，要加强生涯规划教育和选科指导。</p>
              <p>2022年，24个省市已经出台具体的生涯教育政策文件，明确提出要求初高中开展生涯规划教育。</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 第二个背景点 - 白色背景 -->
    <section class="section background-section-2">
      <div class="section__inner">
        <div class="solution-point">
          <div class="left">
            <div class="title-with-line">
              <span class="title">生涯规划教育是什么？</span>
              <div class="title-underline"></div>
            </div>
            <div class="block">Definition of the Concept</div>
          </div>
          <div class="right">
            <div class="content-text">
              <p>生涯规划教育是运用系统方法，指导学生增强对自我和人生发展的认识与理解，促进学生在成长过程中学会选择、主动适应变化和开展生涯规划的发展性教育活动。</p>
              <p>生涯规划教育解决方案旨在帮助学生进行生涯探索，增强学生根据自身兴趣专长进行生涯规划和职业选择的能力，为学生的终生发展奠定基础，承载着学校落实新高考改革、完善生涯教育的课程体系结构、推动家校合作、立体化指导学生的生涯规划、提高高中生涯规划教育效果的重要任务。</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// 无需额外逻辑
</script>

<style scoped>
.section {
  padding: 80px 0;
}

.title-section {
  background-color: #F8F9FA;
  padding: 40px 0 0 0;
}

.background-section-1 {
  background-color: #F8F9FA;
  padding-top: 40px;
}

.background-section-2 {
  background-color: white;
}

.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 36px;
  font-weight: bold;
  color: #262626;
  text-align: center;
  margin-bottom: 0;
}

.solution-point {
  display: flex;
  align-items: flex-start;
  gap: 100px;
}

.left {
  flex: 0 0 350px;
}

.right {
  flex: 1;
}

.title-with-line {
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  line-height: 1.4;
  display: block;
  margin-bottom: 15px;
}

.title-underline {
  width: 100%;
  max-width: 300px;
  height: 2px;
  background-color: #262626;
  margin-bottom: 20px;
}

.block {
  background-color: transparent;
  color: #666;
  padding: 0;
  font-size: 14px;
  font-weight: normal;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: inline-block;
}

.content-text {
  font-size: 16px;
  line-height: 1.8;
  color: #262626;
}

.content-text p {
  margin-bottom: 16px;
}

.content-text p:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media screen and (max-width: 960px) {
  .solution-point {
    flex-direction: column;
    gap: 40px;
  }
  
  .left {
    flex: none;
  }
  
  .section {
    padding: 60px 0;
  }
  
  .title-section {
    padding: 30px 0 0 0;
  }
  
  .background-section-1 {
    padding-top: 30px;
  }
  
  .section-title {
    font-size: 28px;
  }
}

@media screen and (max-width: 768px) {
  .section__inner {
    padding: 0 16px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .content-text {
    font-size: 14px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .section {
    padding: 40px 0;
  }
  
  .title-section {
    padding: 20px 0 0 0;
  }
  
  .background-section-1 {
    padding-top: 20px;
  }
}

@media screen and (max-width: 480px) {
  .title {
    font-size: 18px;
  }
  
  .section-title {
    font-size: 20px;
  }
  
  .solution-point {
    gap: 30px;
  }
  
  .section {
    padding: 30px 0;
  }
}
</style>