<template>
  <div>
    <!-- 方案介绍 -->
    <section class="section b3">
      <div class="section__inner">
        <h2 class="section-title">
          <span>生涯规划教育 · 方案介绍</span>
        </h2>
        <div class="overall">
          <img class="left" :src="introductionImage" alt="高中生涯规划教育解决方案架构">
          <div class="right">
            <div class="details">
              <div class="title">
                <span>「云端平台、智能设备、课程活动」<br>一体化生涯规划教育建设</span>
              </div>
              <div class="title2">
                <span>试界教育持续创新研发生涯规划教育产品，为学校提供一站式的生涯规划教育与选科指导解决方案。</span>
              </div>
              <div class="desc">
                <div class="p">
                  <div class="check-icon"></div>
                  <span>一站式全维度问题解决</span>
                </div>
                <div class="p">
                  <div class="check-icon"></div>
                  <span>高质量优服务成果交付</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 方案优势 -->
    <section class="section b4">
      <div class="section__inner">
        <h2>
          <span>生涯规划教育 · 方案优势</span>
        </h2>
        <div class="product-advantage">
          <div class="advantage">
            <div class="advantage-icon">
              <div class="icon-circle">
                <div class="check-mark">✓</div>
              </div>
            </div>
            <div class="advantage-content">
              <h3>专业团队</h3>
              <p>拥有专业的生涯规划教育团队，具备丰富的实践经验和理论基础</p>
            </div>
          </div>
          
          <div class="advantage">
            <div class="advantage-icon">
              <div class="icon-circle">
                <div class="check-mark">✓</div>
              </div>
            </div>
            <div class="advantage-content">
              <h3>完整方案</h3>
              <p>提供从理论到实践的完整解决方案，涵盖软硬件、课程、培训等各个环节</p>
            </div>
          </div>
          
          <div class="advantage">
            <div class="advantage-icon">
              <div class="icon-circle">
                <div class="check-mark">✓</div>
              </div>
            </div>
            <div class="advantage-content">
              <h3>定制服务</h3>
              <p>根据学校实际需求和特色，提供个性化的定制服务和解决方案</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 获取方案介绍图片
const introductionImage = computed(() => {
  try {
    return new URL('../../assets/images/img-solution-careerEducation-introduce.svg', import.meta.url).href
  } catch (error) {
    console.warn('Introduction image not found')
    return ''
  }
})
</script>

<style scoped>
.section {
  padding: 80px 0;
}

.section.b3 {
  background-color: #F8F9FA;
}

.section.b4 {
  background-color: white;
}

.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h2 {
  font-size: 36px;
  font-weight: bold;
  color: #262626;
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 36px;
  font-weight: bold;
  color: #262626;
  text-align: center;
  margin-bottom: 60px;
}

.overall {
  display: flex;
  align-items: center;
  gap: 80px;
}

.left {
  flex: 0 0 45%;
  width: 100%;
  height: auto;
  object-fit: contain;
}

.right {
  flex: 1;
}

.details .title {
  font-size: 28px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 24px;
  line-height: 1.4;
}

.details .title2 {
  font-size: 18px;
  color: #666;
  margin-bottom: 32px;
  line-height: 1.6;
}

.desc {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.desc .p {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #262626;
}

.check-icon {
  width: 20px;
  height: 20px;
  background-color: #334CD5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.check-icon::before {
  content: '✓';
}

/* 方案优势样式 */
.product-advantage {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.advantage {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 30px;
  background-color: #F8F9FA;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.advantage:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  flex-shrink: 0;
}

.icon-circle {
  width: 60px;
  height: 60px;
  background-color: #E1E4F9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-circle::before {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: #334CD5;
  border-radius: 50%;
}

.check-mark {
  color: white;
  font-size: 20px;
  font-weight: bold;
  z-index: 1;
  position: relative;
}

.advantage-content h3 {
  font-size: 20px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 12px;
}

.advantage-content p {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

/* 响应式设计 */
@media screen and (max-width: 960px) {
  .overall {
    flex-direction: column;
    gap: 40px;
  }
  
  .left {
    flex: none;
    order: -1;
  }
  
  .section {
    padding: 60px 0;
  }
  
  h2 {
    font-size: 28px;
    margin-bottom: 40px;
  }
  
  .section-title {
    font-size: 28px;
    margin-bottom: 40px;
  }
  
  .product-advantage {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media screen and (max-width: 768px) {
  .section__inner {
    padding: 0 16px;
  }
  
  .details .title {
    font-size: 24px;
  }
  
  .details .title2 {
    font-size: 16px;
  }
  
  .desc .p {
    font-size: 14px;
  }
  
  h2 {
    font-size: 24px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .advantage {
    padding: 20px;
  }
}

@media screen and (max-width: 480px) {
  .section {
    padding: 40px 0;
  }
  
  .details .title {
    font-size: 20px;
  }
  
  h2 {
    font-size: 20px;
  }
  
  .advantage {
    flex-direction: column;
    text-align: center;
  }
}
</style>