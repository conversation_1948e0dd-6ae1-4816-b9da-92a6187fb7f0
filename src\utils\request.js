import axios from 'axios'
import { ElMessage } from 'element-plus'
import config from '../config/env'

// 创建axios实例
const service = axios.create({
  baseURL: config.baseApi,
  timeout: 8000
})

// 请求拦截
service.interceptors.request.use(
  (request) => {
    // 从本地存储获取token
    const token = localStorage.getItem(`${config.namespace}-token`)
    if (token) {
      request.headers.Authorization = `Bearer ${token}`
    }
    return request
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截
service.interceptors.response.use(
  (response) => {
    const { code, data, msg } = response.data
    if (code === 200) {
      return data
    } else {
      // 业务错误
      ElMessage.error(msg || '请求失败')
      return Promise.reject(msg || '请求失败')
    }
  },
  (error) => {
    // 处理HTTP错误
    const { status } = error.response
    let message = ''
    switch (status) {
      case 401:
        message = '未授权，请重新登录'
        break
      case 403:
        message = '拒绝访问'
        break
      case 404:
        message = '请求地址错误'
        break
      case 500:
        message = '服务器内部错误'
        break
      default:
        message = '网络连接异常'
    }
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

/**
 * 请求核心函数
 * @param {*} options 请求配置
 */
function request(options) {
  options.method = options.method || 'get'
  if (options.method.toLowerCase() === 'get') {
    options.params = options.data
  }
  
  // 是否使用mock
  if (config.mock) {
    options.baseURL = config.mockApi
  }
  
  return service(options)
}

// 封装常用请求方法
['get', 'post', 'put', 'delete', 'patch'].forEach((method) => {
  request[method] = (url, data, options) => {
    return request({
      url,
      method,
      data,
      ...options
    })
  }
})

export default request 