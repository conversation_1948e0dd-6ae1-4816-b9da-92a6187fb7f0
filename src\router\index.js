import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHashHistory('/publicize/'),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue')
    },
    {
      path: '/contact-us',
      name: 'contact',
      component: () => import('../views/ContactView.vue')
    },
    {
      path: '/solution',
      name: 'solution',
      component: () => import('../views/solution/SolutionView.vue')
    },
    {
      path: '/solution/:id',
      name: 'solution-detail',
      component: () => import('../views/solution/SolutionDetailView.vue')
    },
    {
      path: '/product',
      name: 'product',
      component: () => import('../views/product/ProductView.vue')
    },
    {
      path: '/case',
      name: 'case',
      component: () => import('../views/CaseView.vue')
    },
    {
      path: '/product/:id',
      name: 'product-detail',
      component: () => import('../views/product/ProductDetailView.vue')
    }
  ]
})

export default router 
