<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 导入所有解决方案图片
import shengyaImage from '@/assets/images/solution-shengyajiaoyu.png'
import xinliImage from '@/assets/images/solution-xinlijiankang.png'
import xueyeImage from '@/assets/images/solution-xueyezhidao.png'
import fazhanImage from '@/assets/images/solution-xueshengfazhan.png'

const router = useRouter()

// 解决方案数据
const solutions = ref([
  {
    id: 1,
    title: '生涯教育与选科指导解决方案',
    desc: '助力学校实施职业生涯规划教育，包含云平台、空间建设、课程活动、师资培训，解决高中生涯规划难题',
    image: shengyaImage,
    path: '/solution/career-education'
  },
  {
    id: 2,
    title: '心理健康解决方案',
    desc: '试界教育针对区域、学校定制心理健康解决方案，服务涵盖心理测评、心理课堂，赋能学生发展指导',
    image: xinliImage,
    path: '/solution/mental-health'
  },
  {
    id: 3,
    title: '学业指导解决方案',
    desc: '试界自主研发学习能力测评系统与体系化指导微课，助力中学学业指导工作，培育终生学习能力',
    image: xueyeImage,
    path: '/solution/academic-advising'
  },
  {
    id: 4,
    title: '学生发展指导中心建设解决方案',
    desc: '基于丰富的学生发展指导中心建设经验，提供学生发展指导专用设备、文化装饰一站式解决方案',
    image: fazhanImage,
    path: '/solution/student-development-center'
  }
])

// 特色解决方案
const featuredSolution = ref({
  title: '高中智能生涯教室解决方案',
  desc: '试界教育基于大量学生发展指导中心的建设经验，结合领先的学生发展指导理念与学校特色，提供学生发展指导中心理念顶层设计、文化设计、装修改造等一站式解决方案。',
  image: 'solution-career-classroom.png',
  path: '/solution/career-classroom'
})

// 跳转到解决方案详情页
const goToSolutionDetail = (path) => {
  router.push(path)
}
</script>

<template>
  <section class="solution-section">
    <div class="container section-container">
      <h2 class="section-title">生涯规划教育与学生发展指导一站式解决方案</h2>
    </div>
    
    <div class="solution-overview">
      <div class="container overview-container">
        <div class="overview-image-wrapper">
          <img src="@/assets/images/img-overall.png" alt="学生发展指导解决方案架构图" class="overview-image">
        </div>
        <div class="overview-content">
          <div class="overview-right-panel">
            <div class="divider-line"></div>
            <h3 class="overview-panel-title">面向学生发展指导全方位维度</h3>
            <p class="overview-panel-desc">
              试界教育沉淀多年实践探索，结合不同学校需求特点，设计综合、专业、创新的学生发展指导建设方案。
            </p>
            <p class="overview-panel-desc">
              适配多维度场景，涵盖职业生涯规划教育、心理健康教育、学业指导领域等。
            </p>
            <div class="divider-line"></div>
            <div class="overview-btn" @click="goToSolutionDetail('/solution')">
              <span>查看详情</span>
              <i class="arrow-right-blue"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  
  </section>
  <div class="container">
    <div class="solution-card-grid">
      <div v-for="solution in solutions" :key="solution.id" class="solution-card">
        <div class="solution-card-image"
             :style="{ backgroundImage: `url(${solution.image})` }">
          <div class="solution-card-overlay">
            <h3 class="overlay-title">{{ solution.title }}</h3>
            <div class="overlay-desc-wrapper">
              <p class="overlay-desc">{{ solution.desc }}</p>
            </div>
            <div class="solution-detail-btn" @click.stop="goToSolutionDetail(solution.path)">
              <span>查看详情</span>
              <i class="arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.solution-section {
  padding: 80px 0;
  background-color: #130472;
}

.section-container {
  margin-bottom: 50px;
}

.section-title {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  text-align: center;
}

.solution-overview {
  display: flex;
  align-items: stretch;
  background-color: #130472;
  border-radius: 0;
  overflow: hidden;
  margin-bottom: 0;
  width: 100%;
  position: relative;
}

.overview-container {
  display: flex;
  width: 100%;
}

.overview-image-wrapper {
  width: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #130472;
}

.overview-image {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.overview-content {
  width: 50%;
  padding: 40px;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.overview-right-panel {
  display: flex;
  flex-direction: column;
  padding: 0 30px;
}

.divider-line {
  width: 100%;
  height: 1px;
  background-color: #fff;
  margin: 20px 0;
}

.overview-panel-title {
  font-size: 28px;
  font-weight: bold;
  margin: 20px 0;
  color: #fff;
}

.overview-panel-desc {
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 15px;
  color: #fff;
}

.overview-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #3857E9;
  color: #fff;
  padding: 12px 40px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 0;
  margin-top: 20px;
  font-size: 16px;
}

.overview-btn:hover {
  background-color: #2A43C0;
}

.arrow-right-blue {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  margin-left: 12px;
  filter: brightness(0) invert(1);
}

/* 新的卡片式布局 */
.solution-card-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 60px 0;
}

.solution-card {
  position: relative;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 360px;
}

.solution-card-image {
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
}

.solution-card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px;
  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 70%, rgba(0,0,0,0) 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.solution-card:hover .solution-card-overlay {
  background-color: #130472;
  height: auto;
  bottom: 0;
  top: auto;
}

.overlay-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: white;
}

.overlay-desc-wrapper {
  transition: all 0.3s ease;
}

.overlay-desc {
  font-size: 14px;
  line-height: 1.5;
  display: block; /* 修改为block以确保完整显示 */
  overflow: visible; /* 确保文字不会被截断 */
  opacity: 0.9;
  padding: 10px;
  word-break: break-word; /* 防止长单词导致溢出 */
}

.solution-detail-btn {
  position: relative;
  right: auto;
  bottom: auto;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #FFD700;
  color: #333;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  margin-top: 15px;
  align-self: center; /* 让按钮居中 */
  opacity: 0;  /* 默认隐藏 */
  transition: opacity 0.3s ease;  /* 添加过渡效果 */
}

.solution-card:hover .solution-detail-btn {
  opacity: 1;  /* 鼠标悬停时显示 */
}

.arrow-right {
  display: inline-block;
  width: 20px;
  height: 12px;
  margin-left: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='12' viewBox='0 0 20 12' fill='none'%3E%3Cpath d='M19.5303 6.53033C19.8232 6.23744 19.8232 5.76256 19.5303 5.46967L14.7574 0.696699C14.4645 0.403806 13.9896 0.403806 13.6967 0.696699C13.4038 0.989593 13.4038 1.46447 13.6967 1.75736L17.9393 6L13.6967 10.2426C13.4038 10.5355 13.4038 11.0104 13.6967 11.3033C13.9896 11.5962 14.4645 11.5962 14.7574 11.3033L19.5303 6.53033ZM0 6.75H19V5.25H0V6.75Z' fill='black'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

@media screen and (max-width: 1200px) {
  .solution-card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 992px) {
  .solution-overview,
  .featured-solution {
    flex-direction: column;
  }
  
  .overview-image-wrapper,
  .overview-content,
  .featured-content,
  .featured-image {
    width: 100%;
  }
  
  .overview-image {
    height: auto;
  }
  
  .featured-image {
    order: -1;
  }
}

@media screen and (max-width: 768px) {
  .solution-card-grid {
    grid-template-columns: 1fr;
  }
}
</style> 