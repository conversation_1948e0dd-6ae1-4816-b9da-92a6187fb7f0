<template>
  <div class="solution-page">
    <!-- 头部横幅 -->
    <HeaderBanner />
    
    <!-- 解决方案展示区域 -->
    <SolutionDisplay :solutions="solutions" />
    

  </div>
</template>

<script setup>
import { ref } from 'vue'
import HeaderBanner from '@/components/solution/HeaderBanner.vue'
import SolutionDisplay from '@/components/solution/SolutionDisplay.vue'


// 解决方案数据
const solutions = ref([
  {
    id: 'career-classroom',
    title: '高中智能生涯教室解决方案',
    description: '试界教育基于大量学生发展指导中心的建设经验，结合领先的学生发展指导理念与学校特色，提供学生发展指导中心理念顶层设计、文化设计、装修改造等一站式解决方案。',
    image: 'solution-career-classroom.png',
    path: '/solution/career-classroom',
    isNew: true
  },
  {
    id: 'career-education',
    title: '生涯教育与选科指导解决方案',
    description: '助力学校实施职业生涯规划教育，包含云平台、空间建设、课程活动、师资培训，解决高中生涯规划难题',
    image: 'solution-shengyajiaoyu.png',
    path: '/solution/career-education'
  },
  {
    id: 'mental-health',
    title: '心理健康解决方案',
    description: '试界教育针对区域、学校定制心理健康解决方案，服务涵盖心理测评、心理课堂，赋能学生发展指导',
    image: 'solution-xinlijiankang.png',
    path: '/solution/mental-health'
  },
  {
    id: 'academic-advising',
    title: '学业指导解决方案',
    description: '试界自主研发学习能力测评系统与体系化指导微课，助力中学学业指导工作，培育终生学习能力',
    image: 'solution-academic-advising.png',
    path: '/solution/academic-advising'
  },
  {
    id: 'student-development-center',
    title: '学生发展指导中心建设解决方案',
    description: '基于丰富的学生发展指导中心建设经验，提供学生发展指导专用设备、文化装饰一站式解决方案',
    image: 'solution-sdc-center.png',
    path: '/solution/student-development-center'
  }
])
</script>

<style scoped>
.solution-page {
  min-height: 100vh;
}
</style>