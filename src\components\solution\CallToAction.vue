<template>
  <section class="call-to-action">
    <div class="section__inner">
      <h2>
        <span>现在开始</span><br>
        <span>轻松部署生涯规划教育与学生发展指导体系</span>
      </h2>
      <router-link to="/contact-us" class="cta-link">
        <div class="button">
          <span>产品咨询</span>
        </div>
      </router-link>
    </div>
  </section>
</template>

<script setup>
// 无需额外的逻辑
</script>

<style scoped>
.call-to-action {
  background-color: #062149;
  padding: 80px 0;
  text-align: center;
  color: white;
}

.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h2 {
  font-size: 36px;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 40px;
}

h2 span:first-child {
  display: block;
  margin-bottom: 8px;
}

.cta-link {
  text-decoration: none;
}

.button {
  display: inline-block;
  background-color: #334CD5;
  color: white;
  padding: 16px 40px;
  font-size: 18px;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.button:hover {
  background-color: #2A43C0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(51, 76, 213, 0.3);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .call-to-action {
    padding: 60px 0;
  }
  
  .section__inner {
    padding: 0 16px;
  }
  
  h2 {
    font-size: 28px;
    margin-bottom: 32px;
  }
  
  .button {
    font-size: 16px;
    padding: 14px 32px;
  }
}

@media screen and (max-width: 480px) {
  h2 {
    font-size: 24px;
  }
  
  .button {
    font-size: 14px;
    padding: 12px 24px;
  }
}
</style>