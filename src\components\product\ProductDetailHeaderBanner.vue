<template>
  <section class="section banner-video">
    <div class="banner-bg"></div>
    <img class="banner-bg-top" src="@/assets/images/ele-careerSys-banner-top-b64a15e835dc6ee188dc805254c96436.png" alt="top-bg" />
    <img class="banner-bg-bottom" src="@/assets/images/ele-careerSys-banner-bottom-89cf7306c64ed1cc334aac15a2f2aefa.png" alt="bottom-bg" />
    <div class="banner-inner">
      <div class="banner-left">
        <div class="breadcrumb light">
          <div class="breadcrumb__inner">
            <span>
              <router-link class="breadcrumb__item" to="/">
                <span>首页</span>
              </router-link>
              <span class="breadcrumb__seperate">/</span>
            </span>
            <span>
              <router-link class="breadcrumb__item" to="/product">
                <span>产品</span>
              </router-link>
              <span class="breadcrumb__seperate">/</span>
            </span>
            <span class="breadcrumb__item">
              <span>{{ productTitle }}</span>
            </span>
          </div>
        </div>
        <div class="banner-divider"></div>
        <h1 class="banner-title">试界职业生涯规划系统平台</h1>
        <div class="banner-desc">生涯规划云平台，由试界教育自主研发，快速助力学生生涯教育和升学指导，协助学校有效开展生涯工作。</div>
        <div class="banner-divider"></div>
      </div>
      <div class="banner-right">
        <video class="banner-video-player" controls poster="@/assets/images/product-header-banner-465408453888f401cbe4d4745a4f29a2.png">
          <source src="@/assets/images/01.mp4" type="video/mp4">
          您的浏览器不支持 video 标签。
        </video>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  productTitle: {
    type: String,
    required: true
  },
  productDescription: {
    type: String,
    required: true
  }
})
</script>

<style scoped>
.section.banner-video {
  position: relative;
  background: #e9ebf3;
  min-height: 500px;
  height: 500px;
  display: flex;
  align-items: flex-start;
  margin-top: 100px;
  overflow: hidden;
}
.banner-bg {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: #e9ebf3;
  z-index: 1;
}
.banner-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 600px;
  max-width: 45vw;
  z-index: 2;
  pointer-events: none;
  user-select: none;
}
.banner-bg-bottom {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 700px;
  max-width: 55vw;
  z-index: 2;
  pointer-events: none;
  user-select: none;
}
.banner-inner {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}
.banner-left {
  flex: 1;
  min-width: 420px;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 0;
  padding-left: 0;
  position: relative;
}
.breadcrumb {
  position: absolute;
  top: -38px;
  left: 0;
  margin: 0;
  font-size: 13px;
  color: #222;
  opacity: 1;
  font-weight: 400;
  background: transparent;
  z-index: 10;
}
.breadcrumb__inner {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #222;
  opacity: 1;
  font-weight: 400;
}
.banner-title {
  font-size: 38px;
  font-weight: bold;
  color: #222;
  margin-bottom: 18px;
  margin-top: 10px;
  line-height: 1.2;
  text-shadow: 0 1px 6px rgba(0,0,0,0.10);
}
.banner-desc {
  font-size: 18px;
  color: #444;
  margin-bottom: 18px;
  line-height: 1.7;
  text-shadow: 0 1px 6px rgba(0,0,0,0.06);
}
.banner-divider {
  border-bottom: 3px solid #222;
  width: 100%;
  margin: 24px 0 24px 0;
  opacity: 1;
}
.banner-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-width: 320px;
  max-width: 600px;
}
.banner-video-player {
  width: 500px;
  height: 280px;
  max-width: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  background: #000;
}
.breadcrumb__item, .breadcrumb__seperate {
  color: #222;
  opacity: 1;
}
@media (max-width: 1000px) {
  .section.banner-video {
    height: auto;
    min-height: 400px;
    padding: 40px 0;
  }
  .banner-inner {
    flex-direction: column;
    padding: 32px 8px 0 8px;
    height: auto;
  }
  .banner-left, .banner-right {
    min-width: 0;
    max-width: 100%;
    margin-top: 0;
  }
  .banner-bg-top {
    width: 80vw;
    max-width: 100vw;
  }
  .banner-bg-bottom {
    width: 90vw;
    max-width: 100vw;
  }
  .banner-video-player {
    width: 100%;
    margin-top: 24px;
  }
}
</style> 