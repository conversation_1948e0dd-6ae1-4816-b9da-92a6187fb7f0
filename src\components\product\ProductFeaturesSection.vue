<template>
  <section class="section b2">
    <div class="section__inner">
      <h2>
        <span>{{ productTitle }} · 产品功能</span>
      </h2>
      <div class="display-wrapper">
        <div v-for="(feature, index) in features" :key="index">
          <div class="display" :class="{ 'flex-row-reverse': index % 2 === 1 }">
            <div class="text">
              <div class="detail">
                <div class="title">
                  <span>{{ feature.title }}</span>
                </div>
                <div class="desc">
                  <span v-html="feature.description"></span>
                </div>
              </div>
            </div>
            <img class="image" :src="getImageUrl(feature.image)" :alt="feature.title">
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  productTitle: {
    type: String,
    required: true
  }
})

const features = computed(() => [
  {
    title: '生涯测评系统',
    description: '基于中学生的生涯发展状态，精选权威科学的测评量表。学生通过个体测评结果，对自我有所认识，开启对未来的探索。教师通过团体分析报告，了解学生的生涯发展状况，有针对性地展开指导。',
    image: 'img-shengya-1ed8aebe58ef982a89eb8049631a9315.png'
  },
  {
    title: '选科指导系统',
    description: '通过科学的测评让学生明晰自己的兴趣方向，初步确定自己专业和学校意向，评估合适的选科组合，并进行多轮模拟，锁定意向学科组合。汇总学生数据，生成分析报告，学生、家长可自主查看报告解读，便于学校提前进行教学部署与安排。',
    image: 'img-xuanke-42eb2ce6e6757f1fbea9bc9a2f5e042f.png'
  },
  {
    title: '生涯探索日志系统',
    description: '生涯探索日志方便学生及时记录生涯活动，总结活动反馈，生成个人成长档案，是云平台与综合素质评价的桥梁。同时利于教师管理和输出学生活动成果，生成学校活动材料。',
    image: 'img-rizhi-035fc61df6f9dbf99a34fc9944eb6d32.png'
  },
  {
    title: '生涯教育课程教学系统',
    description: '云平台为教师提供了线上生涯研修班、精品素材汇编和丰富的生涯教材合辑。收集多种生涯教材和生涯教案，提供生涯指导素材，帮助教师理解政策导向，分析实践案例，掌握生涯工具，学习顶层设计，为教师课程设计和日常教学提供便利。',
    image: 'img-jiaoshi-9c78009154d94ee875c9227d7d97ff87.png'
  },
  {
    title: '生涯指导资源系统',
    description: '院校库：汇集全国超2800所院校信息，300多万条招生计划，1000多万条历年录取分数线，学生可自由搜索目标院校。<br>专业库：采用文字与视频结合的大学专业介绍百科，覆盖13个学门，1400多个专业，帮学生深入了解大学专业。<br>职业库：聚焦1000种以上职业的资讯，从工作内容到从业要求，帮助学生建立专业与职业的联系。',
    image: 'img-xuesheng-c12231d836df903324d3375daf433716.png'
  }
])

const getImageUrl = (imageName) => {
  try {
    return new URL(`../../assets/images/${imageName}`, import.meta.url).href
  } catch (error) {
    console.warn(`Product image not found: ${imageName}`)
    return ''
  }
}
</script>

<style scoped>
.section.b2 {
  padding: 80px 0;
  background: white;
}

.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section.b2 h2 {
  text-align: left;
  font-size: 28px;
  font-weight: bold;
  color: #222;
  margin-bottom: 60px;
  margin-left: 0;
}

.display-wrapper {
  display: flex;
  flex-direction: column;
  gap: 100px;
}

.display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.display .text {
  padding: 0 20px;
}

.display .text .detail {
  border-top: 3px solid #222;
  padding-top: 18px;
  margin-bottom: 10px;
}

.display .text .detail .title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  margin-bottom: 18px;
}

.display .text .detail .desc {
  font-size: 16px;
  line-height: 1.6;
  color: #666;
}

.display .image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.display-md {
  display: none;
}

.display.flex-row-reverse {
  direction: rtl;
}
.display.flex-row-reverse > .text {
  direction: ltr;
}
.display.flex-row-reverse > .image {
  direction: ltr;
}

@media (max-width: 768px) {
  .display {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .display-md {
    display: block;
  }
  
  .display .text {
    order: 2;
  }
  
  .display .image {
    order: 1;
  }
  
  .section.b2 h2 {
    font-size: 20px;
  }
  
  .display .text .detail .title {
    font-size: 18px;
  }
}
</style> 