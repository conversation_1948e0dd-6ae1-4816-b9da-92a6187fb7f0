<template>
  <section class="section b1">
    <div class="section__inner">
      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb__inner">
          <router-link to="/" class="breadcrumb__item">
            <span>首页</span>
          </router-link>
          <span class="breadcrumb__seperate">/</span>
          <router-link to="/solution" class="breadcrumb__item">
            <span>解决方案</span>
          </router-link>
          <span class="breadcrumb__seperate">/</span>
          <span class="breadcrumb__item">
            <span>{{ solution.breadcrumb }}</span>
          </span>
        </div>
      </div>
      
      <!-- 主标题 -->
      <h1>
        <span>{{ solution.title }}</span>
      </h1>
      
      <!-- 描述 -->
      <div class="intro">
        <span>{{ solution.description }}</span>
      </div>
    </div>
  </section>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  solution: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.section.b1 {
  background-image: url('@/assets/images/bg-solution-header.png');
  color: white;
  padding: 120px 0 80px;
  margin-top: 100px; /* 为固定导航栏留出空间 */
}

.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 面包屑样式 */
.breadcrumb {
  margin-bottom: 40px;
}

.breadcrumb__inner {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb__item {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb__item:hover {
  color: white;
}

.breadcrumb__seperate {
  margin: 0 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* 标题样式 */
h1 {
  font-size: 42px;
  font-weight: bold;
  margin-bottom: 24px;
  line-height: 1.2;
}

.intro {
  font-size: 18px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  max-width: 800px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .section.b1 {
    padding: 80px 0 60px;
  }
  
  .section__inner {
    padding: 0 16px;
  }
  
  h1 {
    font-size: 28px;
    margin-bottom: 20px;
  }
  
  .intro {
    font-size: 16px;
  }
  
  .breadcrumb {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 480px) {
  h1 {
    font-size: 24px;
  }
  
  .intro {
    font-size: 14px;
  }
}
</style>