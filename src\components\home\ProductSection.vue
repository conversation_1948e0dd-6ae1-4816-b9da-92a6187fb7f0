<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 产品数据
const products = ref([
  {
    id: 1,
    type: '学生发展指导云平台',
    title: '生涯辅导系统',
    engTitle: 'Career System',
    detail: '系统提供个性化的生涯发展诊断和引导服务，丰富的生涯探索内容，精确掌握校内学生的生涯发展状态。',
    path: '/product/career-cloud'
  },
  {
    id: 2,
    type: '学生发展指导云平台',
    title: '学业评估系统',
    engTitle: 'Academic Assessment System',
    detail: '系统帮助教师进行学生个性化学习能力评估，并提供学习能力指导微课，助力学生提高学习能力。',
    path: '/product/academic-cloud'
  },
  {
    id: 3,
    type: '生涯空间专用设备',
    title: 'AI生涯指导舱',
    engTitle: 'AI Career Cabin',
    detail: '通过AI人工智能，解决学生个性化生涯规划、选科指导、学业指导难题，提供AI生涯导师、学业目标透视、学业发展状态测评',
    path: '/product/ai-career-cabin'
  },
  {
    id: 4,
    type: '生涯空间专用设备',
    title: '生涯教室专用设备',
    engTitle: 'Career Device',
    detail: '试界自主研发生涯智能硬件，学生发展指导数据大屏等设备，助力生涯探索室、学生发展指导中心落地。',
    path: '/product/career-device'
  },
  {
    id: 5,
    type: '生涯活动课程',
    title: '学科职业体验课程',
    engTitle: 'Career Experience Course',
    detail: '试界与教授、企业合作，提炼工作典型问题，形成真实体验项目，让中学生深度体验不同专业职业。',
    path: '/product/career-course'
  },
  {
    id: 6,
    type: '生涯活动课程',
    title: '生涯探索游园会',
    engTitle: 'Career Exploration Activity',
    detail: '在校内举办的大型生涯活动，由多个生涯探索游戏组成，帮助学生唤醒生涯意识，了解自我和外部世界。',
    path: '/product/career-activity'
  }
])

// 当前悬停的产品卡片
const hoveredProduct = ref(null)

// 设置悬停的产品
const setHoveredProduct = (product) => {
  hoveredProduct.value = product
}

// 清除悬停的产品
const clearHoveredProduct = () => {
  hoveredProduct.value = null
}

// 跳转到产品详情页
const goToProductDetail = (path) => {
  router.push(path)
}

// 跳转到产品列表页
const goToProductList = () => {
  router.push('/product')
}
</script>

<template>
  <section class="product-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">大学生职业生涯规划服务项目</h2>
        <div class="view-more" @click="goToProductList">
          <span>查看详情</span>
          <i class="arrow-right">→</i>
        </div>
      </div>
      
      <div class="product-grid">
        <div v-for="product in products" :key="product.id" 
             class="product-card"
             @mouseenter="setHoveredProduct(product)"
             @mouseleave="clearHoveredProduct"
             :class="{'hovered': hoveredProduct === product}">
          <div class="product-header">
            <div class="blue-bar"></div>
          </div>
          <div class="product-content">
            <div class="product-type">{{ product.type }}</div>
            <div class="product-title">{{ product.title }}</div>
            <div class="product-eng-title">{{ product.engTitle }}</div>
            
            <!-- 默认状态下显示的按钮 -->
            <div class="arrow-container" v-if="hoveredProduct !== product">
              <div class="arrow-circle">→</div>
            </div>
            
            <!-- 悬停状态下显示的内容 -->
            <div class="hover-content" v-if="hoveredProduct === product">
              <div class="detail-content">{{ product.detail }}</div>
              <div class="detail-link-container" @click="goToProductDetail(product.path)">
                <div class="detail-link">查看详情</div>
                <div class="arrow-icon">→</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.product-section {
  padding: 80px 0;
  background-color: #f9f9f9;
  background-image: linear-gradient(to right bottom, #f9f9f9, #f5f5f5);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.view-more {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #00a4ff;
  font-size: 14px;
  border: 1px solid #00a4ff;
  padding: 8px 16px;
  border-radius: 4px;
}

.view-more:hover {
  background-color: #00a4ff;
  color: #fff;
}

.arrow-right {
  margin-left: 8px;
  font-size: 16px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.product-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  height: 200px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 悬停样式 */
.product-card.hovered {
  background-color: #1e3a8a;
  color: #fff;
  height: 260px;
}

.product-header {
  position: relative;
  padding: 0;
  height: 8px;
}

.blue-bar {
  background-color: #00a4ff;
  height: 8px;
  width: 40px;
  position: absolute;
  left: 0;
  top: 0;
}

.product-card.hovered .blue-bar {
  background-color: #fde047;
}

.product-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-type {
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
}

.product-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.product-eng-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 15px;
}

/* 悬停状态下的文字颜色 */
.product-card.hovered .product-type,
.product-card.hovered .product-title,
.product-card.hovered .product-eng-title {
  color: #fff;
}

/* 默认状态下的箭头 */
.arrow-container {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}

.arrow-circle {
  width: 30px;
  height: 30px;
  background-color: #f0f0f0;
  color: #00a4ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

/* 悬停状态下显示的内容 */
.hover-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.detail-content {
  font-size: 14px;
  line-height: 1.6;
  color: #fff;
  flex: 1;
}

.detail-link-container {
  display: flex;
  align-items: center;
  margin-top: 15px;
  justify-content: flex-end;
}

.detail-link {
  font-size: 14px;
  color: #fff;
}

.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #fde047;
  color: #1e3a8a;
  border-radius: 4px;
  margin-left: 10px;
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .view-more {
    margin-top: 15px;
  }
  
  .product-grid {
    grid-template-columns: 1fr;
  }
}
</style> 