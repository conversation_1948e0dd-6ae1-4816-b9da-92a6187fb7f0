<script setup>
import { ref } from 'vue'
// 导入头像图片
import expertAvatar from '@/assets/images/expert_avator_1.png'

// 专家数据
const experts = ref([
  {
    id: 1,
    name: '侯志瑾',
    title: '教授',
    position: '生涯专家顾问',
    description: '北京师范大学心理学部教授<br>中国心理学会临床与咨询专业委员会委员<br>国际应用心理学会咨询专业委员会中国联络人',
    avatar: expertAvatar
  },
  {
    id: 2,
    name: '金蕾莅',
    title: '博士',
    position: '生涯专家顾问',
    description: '清华大学学生职业发展指导中心副主任<br>清华大学职业发展教育研究室主任<br>亚太职业生涯发展协会(APCDA)中国区负责人',
    avatar: expertAvatar
  },
  {
    id: 3,
    name: '陈冠',
    title: '教授',
    position: '职业体验课程总顾问',
    description: '中国人民大学商学院教授<br>中国商品学会会长<br>北京大学经济研究中心国际 EMBA 访问教授',
    avatar: expertAvatar
  }
])

// 专家观点
const expertQuote = ref({
  name: '侯志瑾 教授',
  title: '中小学生职业生涯发展领域专家',
  content: '基于前沿生涯理论进行中学生涯教育的基础设计，结合 PBL学科职业体验课，试界教育的中学生涯教育整体解决方案为学生提供大学学科和职业的探索途径，具有很强的专业性、科学性和创新性。试界的专业团队将为中学校方提供宝贵的教育价值。'
})
</script>

<template>
  <section class="expert-section">
    <div class="container">
      <h2 class="section-title">专家服务团队领衔职业生涯规划</h2>

      <div class="experts-container">
        <div v-for="expert in experts" :key="expert.id" class="expert-card">
          <div class="expert-avatar-container">
            <img :src="expert.avatar" :alt="expert.name" class="expert-avatar">
          </div>
          <div class="expert-position">{{ expert.position }}</div>
          <div class="expert-name">
            <span>{{ expert.name }}</span>
            <span>{{ expert.title }}</span>
          </div>
          <div class="expert-desc" v-html="expert.description"></div>
        </div>
      </div>

    </div>
  </section>
</template>

<style scoped>
.expert-section {
  padding: 80px 0;
  background-color: #f9f9f9;
}

.section-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 50px;
}

.experts-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 60px;
}

.expert-card {
  width: calc(33.33% - 20px);
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.expert-avatar-container {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 20px;
  border: 2px solid #f0f0f0;
}

.expert-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.expert-position {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.expert-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.expert-name span:last-child {
  margin-left: 5px;
}

.expert-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.expert-quote {
  background-color: #334CD5;
  color: #fff;
  border-radius: 8px;
  padding: 40px;
  position: relative;
}

.expert-quote::before {
  content: '"';
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 80px;
  opacity: 0.2;
}

.quote-title {
  font-size: 18px;
  margin-bottom: 20px;
}

.quote-title span:first-child {
  font-weight: normal;
}

.quote-title span:last-child {
  font-weight: bold;
}

.quote-content {
  font-size: 18px;
  line-height: 1.8;
  font-style: italic;
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .expert-card {
    width: calc(50% - 15px);
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 768px) {
  .expert-card {
    width: 100%;
  }
}
</style> 