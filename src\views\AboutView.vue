<script setup>
// 无需复杂逻辑，仅静态内容展示
</script>

<template>
  <div class="about-page">
    <!-- 顶部横幅 -->
    <div class="banner">
      <div class="banner-bg"></div>
      <div class="container">
        <!-- 添加面包屑导航 -->
        <div class="breadcrumb light">
          <div class="breadcrumb__inner">
            <router-link to="/" class="breadcrumb__item">首页</router-link>
            <span class="breadcrumb__seperate">/</span>
            <span class="breadcrumb__item">关于笔墨屋</span>
          </div>
        </div>
        <div class="banner-content">
          <h1 class="page-title">笔墨屋</h1>
          <p class="page-subtitle">值得信赖的内容创作与文化传播伙伴</p>
        </div>
      </div>
    </div>
    <div class="container">
             <!-- 公司简介 -->
       <div class="about-section">
         <div class="bg-decoration"></div>
         <div class="section-header">
           <h2>走进笔墨屋</h2>
           <div class="section-line"></div>
         </div>
         <div class="section-content">
           <div class="text-content">
             <p>试界教育创立于2017年，专注于中学生涯规划、心理健康及学业指导的研究与实践，为学生发展指导领域提供专业解决方案与产品服务，致力于在中学阶段践行育人兴邦使命。</p>
             <p>在普通高中育人方式改革的政策引领背景下，试界教育坚持以科学理论为依据，结合互联网与人工智能先进技术，围绕学生发展指导、生涯教育、心理健康与学业指导提供整体解决方案，积极推动学生发展指导中心的建设，促进教育创新改革。</p>
             <p>试界教育重视内容与技术的融合，在创新精神驱动下，自主研发并开创性推出AI生涯指导舱、"生涯四部曲"生涯探索一体机、学生发展指导云平台、中学生涯探索游园会、PBL学科职业体验课程、学业目标透视系统等学生发展指导产品。</p>
             <p>目前试界教育的学生发展指导实践已经深入学校一线，为全国200多所中小学校提供解决方案，惠及学生逾三十万名。试界教育受到社会各界认可，先后荣获"国家高新技术企业"、"中关村高新技术企业"、"中关村金种子企业"等称号；"基于生涯教育理念的PBL模式学科职业体验课程"获奖于中国教育创新成果公益博览会。</p>
             <p>试界教育秉承"帮助每一位中学生找到热爱"的理念，将持续深化学生发展指导的研究与实践，为中国中学生的寻梦之旅助力！</p>
           </div>
         </div>
       </div>
      
      <!-- 定位/使命/愿景/价值观 四宫格内容区 -->
      <div class="vision-mission-section-full">
        <div class="vision-mission-section">
          <div class="vision-mission-grid">
            <div class="vision-mission-item">
              <h3>定位 <span>POSITIONING</span></h3>
              <p>职业生涯规划教育与学生发展指导解决方案供应商</p>
            </div>
            <div class="vision-mission-item">
              <h3>使命 <span>MISSION</span></h3>
              <p>帮助每一位中学生找到热爱的事物</p>
            </div>
            <div class="vision-mission-item">
              <h3>愿景 <span>VISION</span></h3>
              <p>聚焦生涯规划教育，助力人才强国建设</p>
            </div>
            <div class="vision-mission-item">
              <h3>价值观 <span>VALUES</span></h3>
              <p>立足现在、胸怀未来、超越创新</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 企业优势 -->
    <div class="advantage-section">
      <div class="container">
        <h2 class="advantage-title-main">试界教育企业优势</h2>
        <div class="advantage-grid">
          <div class="advantage-card-new">
            <img class="advantage-icon" src="../assets/images/19fbff18682e560bcbfe251beeadfd70.svg" alt="全国重点中学认可品牌">
            <div class="advantage-divider"></div>
            <div class="advantage-label">全国重点中学认可品牌</div>
          </div>
          <div class="advantage-card-new">
            <img class="advantage-icon" src="../assets/images/efbfa98ae06bcbd5d39cd66e1e025675.svg" alt="持续创新引领行业">
            <div class="advantage-divider"></div>
            <div class="advantage-label">持续创新引领行业</div>
          </div>
          <div class="advantage-card-new">
            <img class="advantage-icon" src="../assets/images/efbfa98ae06bcbd5d39cd66e1e025675.svg" alt="高层次研发服务团队">
            <div class="advantage-divider"></div>
            <div class="advantage-label">高层次研发服务团队</div>
          </div>
          <div class="advantage-card-new">
            <img class="advantage-icon" src="../assets/images/19fbff18682e560bcbfe251beeadfd70.svg" alt="拥有自主知识产权">
            <div class="advantage-divider"></div>
            <div class="advantage-label">拥有自主知识产权</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
/* 添加面包屑样式 */
.breadcrumb {
  position: relative;
  width: 100%;
}
.breadcrumb .breadcrumb__inner {
  padding: 24px 0;
}
.breadcrumb .breadcrumb__inner .breadcrumb__item {
  display: inline-block;
  font-size: 14px;
  vertical-align: top;
  user-select: none;
}
.breadcrumb .breadcrumb__inner a {
  color: rgba(255, 255, 255, 0.65);
}
.breadcrumb .breadcrumb__inner span {
  color: #ffffff;
}
.breadcrumb .breadcrumb__inner .breadcrumb__seperate {
  user-select: none;
  margin: 0 8px;
  color: #ffffff;
  display: inline-block;
  vertical-align: top;
}
.breadcrumb.light a {
  color: rgba(255, 255, 255, 0.65);
}
.breadcrumb.light span {
  color: #ffffff;
}
.breadcrumb.light .breadcrumb__seperate {
  color: #ffffff;
}
.breadcrumb.dark a {
  color: rgba(0, 0, 0, 0.45);
}
.breadcrumb.dark span {
  color: rgba(0, 0, 0, 0.85);
}
.breadcrumb.dark .breadcrumb__seperate {
  color: rgba(0, 0, 0, 0.45);
}

.about-page {
  padding: 0 0 80px;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.banner {
  position: relative;
  background-color: #07114C; /* 深蓝色背景，与试界页面一致 */
  color: white;
  padding: 120px 0 60px;
  margin-bottom: 60px;
  text-align: left; /* 改为左对齐 */
  overflow: hidden;
  height: 400px; /* 固定高度 */
  display: flex;
  align-items: center;
}
.banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('../assets/images/banner-bg.jpg'); /* 使用现有的背景图片 */
  background-size: cover;
  background-position: center;
  opacity: 100%; /* 降低透明度，让背景图片更加淡化 */
  z-index: 1;
}
.banner-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
}
.page-title {
  font-size: 42px;
  font-weight: bold;
  margin-bottom: 20px;
}
.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}
.about-section {
  margin-bottom: 80px;
  position: relative;
  overflow: hidden;
}

.bg-decoration {
  position: absolute;
  top: -50px;
  left: -100px;
  width: 300px;
  height: 300px;
  background-image: url('../assets/images/bg-about-b1-left-06b8a4730b6ed4188fd2251459e98d38.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: left top;
  opacity: 0.3;
  z-index: 1;
  pointer-events: none;
}
.section-header {
  margin-bottom: 40px;
  text-align: center;
  position: relative;
  z-index: 2;
}
.section-header h2 {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}
.section-line {
  width: 80px;
  height: 4px;
  background: #334CD5;
  margin: 0 auto;
}
.section-content {
  display: flex;
  gap: 40px;
  align-items: center;
  position: relative;
  z-index: 2;
}
.text-content {
  flex: 1;
}
.text-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  margin-bottom: 20px;
}
.image-content {
  flex: 1;
}
.intro-image {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}
.values-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}
.value-card {
  background-color: #f9f9f9;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}
.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.value-icon {
  width: 60px;
  height: 60px;
  background-color: #334CD5;
  border-radius: 50%;
  margin: 0 auto 20px;
}
.value-card h3 {
  font-size: 20px;
  font-weight: bold;
  color: #334CD5;
  margin-bottom: 15px;
}
.value-card p {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}
.advantages-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}
.advantage-card {
  background-color: #f9f9f9;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}
.advantage-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.advantage-img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 15px;
}
.advantage-title {
  font-size: 16px;
  color: #334CD5;
  font-weight: bold;
}
.contact-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}
.contact-info {
  flex: 1;
}
.info-item {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.6;
}
.info-item strong {
  color: #334CD5;
  margin-right: 10px;
}
.contact-qrcode {
  text-align: center;
}
.contact-qrcode img {
  width: 160px;
  height: 160px;
  margin-bottom: 10px;
  border: 1px solid #e0e0e0;
  padding: 5px;
}
.contact-qrcode p {
  font-size: 14px;
  color: #666;
}
@media screen and (max-width: 992px) {
  .section-content {
    flex-direction: column;
  }
  .values-list, .advantages-list {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .bg-decoration {
    width: 200px;
    height: 200px;
    top: -30px;
    left: -50px;
  }
}
@media screen and (max-width: 768px) {
  .banner {
    padding: 100px 0 50px;
  }
  .page-title {
    font-size: 36px;
  }
  .section-header h2 {
    font-size: 28px;
  }
  .contact-content {
    flex-direction: column;
  }
  
  .bg-decoration {
    width: 150px;
    height: 150px;
    top: -20px;
    left: -30px;
    opacity: 0.2;
  }
}
@media screen and (max-width: 576px) {
  .values-list, .advantages-list {
    grid-template-columns: 1fr;
  }
  .page-title {
    font-size: 32px;
  }
  .section-header h2 {
    font-size: 24px;
  }
}

/* 添加图片中样式的定位、使命、愿景、价值观部分样式 */
.vision-mission-section-full {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: none;
}
.vision-mission-section {
  margin: 0;
  background: url('../assets/images/bg-about.jpg') center center no-repeat;
  background-size: cover;
  position: relative;
  overflow: hidden;
  width: 100vw;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

}

.vision-mission-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.18);
  z-index: 1;
}

.vision-mission-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0;
  position: relative;
  z-index: 2;
}

.vision-mission-item {
  padding: 60px 40px;
  color: white;
  position: relative;
}

.vision-mission-item h3 {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 20px;
  position: relative;
}

.vision-mission-item h3 span {
  display: block;
  font-size: 20px;
  opacity: 0.4;
  font-weight: bold;
  margin-top: 5px;
  letter-spacing: 2px;
}

.vision-mission-item p {
  font-size: 20px;
  line-height: 1.6;
  max-width: 90%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .vision-mission-grid {
    grid-template-columns: 1fr;
  }
  
  .vision-mission-item {
    padding: 40px 20px;
  }
  .vision-mission-item h3 {
    font-size: 24px;
  }
  .vision-mission-item h3 span {
    font-size: 14px;
  }
  .vision-mission-item p {
    font-size: 16px;
  }
}

.advantage-section {
  background: #f5f5f5; /* 保持与图片中红色框内背景一致的浅灰色 */
  padding: 60px 0 80px 0;
  margin: 0;
  width: 100vw; /* 占据整个视口宽度 */
  margin-left: calc(-50vw + 50%); /* 让背景色延伸到屏幕边缘 */
  position: relative;
}

.advantage-title-main {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: #111;
  margin-bottom: 48px;
}

.advantage-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px 40px;
  max-width: 1100px;
  margin: 0 auto; /* 保持卡片在中间，但背景色占满 */
  padding: 0 20px; /* 添加左右内边距，确保在小屏幕上有边距 */
}

.advantage-card-new {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.03);
  padding: 48px 0 32px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 260px;
  position: relative;
}

.advantage-icon {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 16px;
}

.advantage-divider {
  width: 60px;
  height: 6px;
  background: #ffe600;
  border-radius: 3px;
  margin: 18px auto 18px auto;
}

.advantage-label {
  font-size: 20px;
  color: #111;
  font-weight: bold;
  margin-top: 0;
}

@media (max-width: 900px) {
  .advantage-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  .advantage-card-new {
    min-height: 180px;
    padding: 32px 0 24px 0;
  }
}
</style>