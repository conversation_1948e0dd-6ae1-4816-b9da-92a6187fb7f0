<script setup>
import { ref, onMounted } from 'vue'
import BannerSection from '../components/home/<USER>'
import ProductSection from '../components/home/<USER>'
import SolutionSection from '../components/home/<USER>'
import ExpertSection from '../components/home/<USER>'
import CaseSection from '../components/home/<USER>'
import ContactSection from '../components/home/<USER>'
</script>

<template>
  <div class='home-page'>
    <BannerSection />
    <ProductSection />
    <SolutionSection />
    <ExpertSection />
    <CaseSection />
    <ContactSection />
  </div>
</template>

<style scoped>
.home-page {
  /* Remove padding to make banner flush with navigation */
}
</style>
