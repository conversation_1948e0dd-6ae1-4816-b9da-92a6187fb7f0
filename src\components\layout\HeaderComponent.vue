<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const activeMenu = ref('home')

// 导航菜单数据
const navMenus = ref([
  { 
    name: '首页', 
    key: 'home', 
    path: '/',
    hasChildren: false
  },
  { 
    name: '服务项目',
    key: 'product', 
    path: '/product',
    hasChildren: true,
    children: [
      {
        title: '学生发展指导云平台',
        items: [
          { name: '生涯辅导系统', path: '/product/career-cloud' },
          { name: '心理健康系统', path: '/product/mental-health-cloud' },
          { name: '学业评估系统', path: '/product/academic-cloud' }
        ]
      },
      {
        title: '生涯空间专用设备',
        items: [
          { name: 'AI生涯指导舱', path: '/product/ai-career-cabin' },
          { name: '学生发展数字大屏', path: '/product/career-device#screen' },
          { name: '生涯探索一体机', path: '/product/career-device#aio' },
          { name: '生涯辅导桌面式一体机', path: '/product/career-device#tablet' }
        ]
      },
      {
        title: '生涯活动课程',
        items: [
          { name: '学科职业体验课程', path: '/product/career-course' },
          { name: '生涯探索游园会', path: '/product/career-activity' }
        ]
      }
    ]
  },
  { 
    name: '解决方案', 
    key: 'solution', 
    path: '/solution',
    hasChildren: true,
    children: [
      {
        title: '智能生涯教室解决方案',
        items: [
          { name: '高中智能生涯教室整体解决方案', path: '/solution/career-classroom', isNew: true }
        ]
      },
      {
        title: '学生发展指导综合解决方案',
        items: [
          { name: '生涯教育与选科指导解决方案', path: '/solution/career-education' },
          { name: '心理健康解决方案', path: '/solution/mental-health' },
          { name: '学业指导解决方案', path: '/solution/academic-advising' },
          { name: '学生发展指导中心建设解决方案', path: '/solution/student-development-center' }
        ]
      }
    ]
  },
  { 
    name: '喜报案例',
    key: 'case', 
    path: '/case',
    hasChildren: false
  },
  { 
    name: '关于笔墨屋',
    key: 'about', 
    path: '/about',
    hasChildren: true,
    children: [
      {
        items: [
          { name: '了解笔墨屋', path: '/about' },
          { name: '合作联系', path: '/contact-us' }
        ]
      }
    ]
  }
])

// 移动端菜单显示控制
const mobileMenuVisible = ref(false)

// 处理菜单点击
const handleMenuClick = (menu) => {
  activeMenu.value = menu.key
  // 只要有 path 就跳转，无论是否有子菜单
  if (menu.path) {
    router.push(menu.path)
    mobileMenuVisible.value = false
  }
}

// 处理子菜单项点击
const handleSubMenuClick = (item) => {
  router.push(item.path)
  mobileMenuVisible.value = false
}

// 切换移动端菜单显示状态
const toggleMobileMenu = () => {
  mobileMenuVisible.value = !mobileMenuVisible.value
}

// 根据当前路由设置激活菜单
const updateActiveMenu = () => {
  const path = route.path
  for (const menu of navMenus.value) {
    if (path === menu.path || path.startsWith(menu.path + '/')) {
      activeMenu.value = menu.key
      break
    }
  }
}

// 监听路由变化
watch(() => route.path, updateActiveMenu, { immediate: true })

// 组件挂载时也执行一次
onMounted(() => {
  updateActiveMenu()
})
</script>

<template>
  <div class="header-wrapper">
    <!-- 顶部深蓝色导航条 -->
    <div class="top-bar">
      <div class="container">
        <router-link to="/" class="back-link">返回笔墨屋首页 →</router-link>
      </div>
    </div>

    <!-- Logo that spans across top bar and header -->
    <div class="logo-container">
      <router-link to="/" class="header__logo">
        <img src="@/assets/images/logo.png" alt="笔墨屋" />
      </router-link>
    </div>

    <header class="header">
      <div class="header__inner container">
        <!-- Logo placeholder to maintain spacing -->
        <div class="logo-placeholder"></div>

        <!-- 导航菜单 -->
        <nav class="desktop-nav" :class="{ 'mobile-visible': mobileMenuVisible }">
          <ul class="nav-list flex">
            <li v-for="menu in navMenus" :key="menu.key" 
                class="nav-item" 
                :class="{ 'active': activeMenu === menu.key, 'has-children': menu.hasChildren }">
              <div class="nav-link" @click="handleMenuClick(menu)">
                {{ menu.name }}
                <i v-if="menu.hasChildren" class="arrow-down"></i>
              </div>
              
              <!-- 子菜单 -->
              <div v-if="menu.hasChildren" class="submenu">
                <div v-for="(section, sIndex) in menu.children" :key="sIndex" class="submenu-section">
                  <div v-if="section.title" class="submenu-title">
                    <img src="@/assets/images/submenu-icon.png" alt="">
                    <span>{{ section.title }}</span>
                  </div>
                  <div class="submenu-items">
                    <div v-for="(item, iIndex) in section.items" :key="iIndex" 
                        class="submenu-item" @click="handleSubMenuClick(item)">
                      <span>{{ item.name }}</span>
                      <div v-if="item.isNew" class="new-tag">New</div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </nav>

        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
        
        <!-- 教师登录按钮 -->
        <div class="login-container">
          <a href="https://xx.shijieu.cn/" target="_blank" class="teacher-login">登录</a>
        </div>
      </div>
    </header>
  </div>
</template>

<style scoped>
.header-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.top-bar {
  background-color: #001440;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.top-bar .container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.back-link {
  color: #fff;
  font-size: 14px;
  text-decoration: none;
}

.back-link:hover {
  text-decoration: underline;
}

/* Logo container that spans across top bar and header */
.logo-container {
  position: absolute;
  top: 0;
  left: 180px; /* 调整logo位置，放在左侧一点，与图片一致 */
  width: 190px; /* Width of the logo container */
  height: 100px; /* Height spanning both top bar and header */
  background-color: transparent; /* Remove blue background for logo */
  z-index: 101;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header__logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.header__logo img {
  width: 100%; /* Make the logo fill the container */
  height: 100%;
}

/* Placeholder to maintain spacing in the header */
.logo-placeholder {
  width: 230px; /* 减小占位符宽度，使菜单整体向左移动 */
}

.header {
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.header__inner {
  height: 100%;
  display: flex;
  align-items: center;
}

.desktop-nav {
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  margin-left: -40px; /* 向左移动导航菜单 */
}

.nav-list {
  height: 100%;
  display: flex;
  margin: 0;
  padding: 0;
  list-style: none;
}

.nav-item {
  position: relative;
  height: 100%;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 120px; /* 统一菜单项宽度，以"解决方案"为标准 */
  justify-content: center; /* 居中菜单文本 */
}

.nav-item.active .nav-link {
  color: #00a4ff;
}

/* 添加蓝色线条标识当前选中的菜单项 */
.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #00a4ff;
}

.nav-link {
  font-size: 14px; /* 缩小导航栏菜单字体大小 */
  display: flex;
  align-items: center;
  color: #333;
  text-align: center;
}

.arrow-down {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #333;
  margin-left: 4px;
}

.nav-item.active .arrow-down {
  border-top-color: #00a4ff;
}

.login-container {
  margin-left: auto;
}

.submenu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  display: none;
  padding: 20px;
  z-index: 10;
}

.nav-item:hover .submenu {
  display: flex;
}

.submenu-section {
  margin-right: 30px;
}

.submenu-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
  font-size: 13px; /* 缩小子菜单标题字体大小 */
}

.submenu-title img {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.submenu-item {
  padding: 8px 0;
  white-space: nowrap;
  position: relative;
  font-size: 13px; /* 缩小子菜单项字体大小 */
}

.submenu-item:hover {
  color: #00a4ff;
}

.new-tag {
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #ff4d4f;
  color: #fff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.teacher-login {
  padding: 8px 16px;
  background-color: transparent;
  color: #00a4ff;
  border: 1px solid #00a4ff;
  border-radius: 4px;
  font-size: 14px;
  text-decoration: none;
}

.teacher-login:hover {
  background-color: #00a4ff;
  color: #fff;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 24px;
  height: 18px;
  cursor: pointer;
  margin-left: auto;
}

.mobile-menu-btn span {
  display: block;
  height: 2px;
  background-color: #333;
  transition: all 0.3s;
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .logo-container {
    width: 150px;
    height: 80px;
    left: 0; /* 在小屏幕上恢复到左侧 */
  }

  .logo-placeholder {
    width: 150px;
  }
  
  .mobile-menu-btn {
    display: flex;
  }

  .desktop-nav {
    position: fixed;
    top: 100px; /* 调整为顶部栏+导航栏高度 */
    left: 0;
    width: 100%;
    height: calc(100vh - 100px);
    background-color: #fff;
    flex-direction: column;
    align-items: flex-start;
    padding: 20px;
    transform: translateX(100%);
    transition: transform 0.3s;
    overflow-y: auto;
    margin-left: 0; /* 在移动端移除左边距 */
  }

  .desktop-nav.mobile-visible {
    transform: translateX(0);
  }

  .nav-list {
    flex-direction: column;
    width: 100%;
    height: auto;
  }

  .nav-item {
    width: 100%;
    height: auto;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    justify-content: flex-start; /* 在移动端左对齐 */
  }

  /* 在移动端调整蓝色线条的位置 */
  .nav-item.active::after {
    bottom: -1px;
    height: 2px;
  }

  .submenu {
    position: static;
    box-shadow: none;
    display: none;
    padding: 10px 0 0 20px;
  }

  .nav-item.active .submenu {
    display: block;
  }

  .submenu-section {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .login-container {
    margin: 20px 0 0;
    width: 100%;
  }

  .teacher-login {
    display: block;
    width: 100%;
    text-align: center;
  }
}
</style> 
