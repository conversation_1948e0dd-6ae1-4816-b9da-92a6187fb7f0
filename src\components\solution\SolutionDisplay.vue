<template>
  <div class="display-wrapper">
    <section 
      v-for="(solution, index) in solutions" 
      :key="solution.id"
      :class="['section', 'vertical-display-section', { 'is-odd': index % 2 === 0 }]"
    >
      <div class="section__inner">
        <div class="display">
          <!-- 奇数项：文字在左，图片在右 -->
          <template v-if="index % 2 === 0">
            <div class="text">
              <div class="detail">
                <div class="title">
                  <span>{{ solution.title }}</span>
                  <div v-if="solution.isNew" class="new-tips">New</div>
                </div>
                <div class="desc">
                  <span>{{ solution.description }}</span>
                </div>
              </div>
              <div class="operation">
                <router-link :to="solution.path">
                  <div class="btn">
                    <span>查看详情</span>
                    <svg class="icon" viewBox="0 0 1024 1024" width="20" height="20">
                      <path d="M885.113 489.373L628.338 232.599c-12.496-12.497-32.758-12.497-45.254 0-12.497 12.497-12.497 32.758 0 45.255l203.3 203.3H158.025c-17.036 0-30.846 13.811-30.846 30.846 0 17.036 13.811 30.846 30.846 30.846h628.36L583.084 746.147c-12.497 12.496-12.497 32.758 0 45.255 6.248 6.248 14.438 9.372 22.627 9.372s16.379-3.124 22.627-9.372l256.775-256.775a31.999 31.999 0 0 0 0-45.254z" fill="#334CD5"/>
                    </svg>
                  </div>
                </router-link>
              </div>
            </div>
            <img class="image" :src="getSolutionImage(solution.image)" :alt="solution.title">
          </template>
          
          <!-- 偶数项：图片在左，文字在右 -->
          <template v-else>
            <img class="image" :src="getSolutionImage(solution.image)" :alt="solution.title">
            <div class="text">
              <div class="detail">
                <div class="title">
                  <span>{{ solution.title }}</span>
                  <div v-if="solution.isNew" class="new-tips">New</div>
                </div>
                <div class="desc">
                  <span>{{ solution.description }}</span>
                </div>
              </div>
              <div class="operation">
                <router-link :to="solution.path">
                  <div class="btn">
                    <span>查看详情</span>
                    <svg class="icon" viewBox="0 0 1024 1024" width="20" height="20">
                      <path d="M885.113 489.373L628.338 232.599c-12.496-12.497-32.758-12.497-45.254 0-12.497 12.497-12.497 32.758 0 45.255l203.3 203.3H158.025c-17.036 0-30.846 13.811-30.846 30.846 0 17.036 13.811 30.846 30.846 30.846h628.36L583.084 746.147c-12.497 12.496-12.497 32.758 0 45.255 6.248 6.248 14.438 9.372 22.627 9.372s16.379-3.124 22.627-9.372l256.775-256.775a31.999 31.999 0 0 0 0-45.254z" fill="#334CD5"/>
                    </svg>
                  </div>
                </router-link>
              </div>
            </div>
          </template>
        </div>
        
        <!-- 移动端专用布局 -->
        <div class="display-md">
          <div class="text">
            <div class="detail">
              <div class="title">
                <span>{{ solution.title }}</span>
                <div v-if="solution.isNew" class="new-tips">New</div>
              </div>
              <div class="desc">
                <span>{{ solution.description }}</span>
              </div>
            </div>
            <div class="operation">
              <router-link :to="solution.path">
                <div class="btn">
                  <span>查看详情</span>
                  <svg class="icon" viewBox="0 0 1024 1024" width="20" height="20">
                    <path d="M885.113 489.373L628.338 232.599c-12.496-12.497-32.758-12.497-45.254 0-12.497 12.497-12.497 32.758 0 45.255l203.3 203.3H158.025c-17.036 0-30.846 13.811-30.846 30.846 0 17.036 13.811 30.846 30.846 30.846h628.36L583.084 746.147c-12.497 12.496-12.497 32.758 0 45.255 6.248 6.248 14.438 9.372 22.627 9.372s16.379-3.124 22.627-9.372l256.775-256.775a31.999 31.999 0 0 0 0-45.254z" fill="#334CD5"/>
                  </svg>
                </div>
              </router-link>
            </div>
          </div>
          <img class="image" :src="getSolutionImage(solution.image)" :alt="solution.title">
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  solutions: {
    type: Array,
    required: true
  }
})

// 获取解决方案图片
const getSolutionImage = (imageName) => {
  try {
    return new URL(`../../assets/images/${imageName}`, import.meta.url).href
  } catch (error) {
    console.warn(`Image not found: ${imageName}`)
    return ''
  }
}
</script>

<style scoped>
.display-wrapper {
  background-color: #fff;
}

.section {
  padding: 80px 0;
}

.section.is-odd {
  background-color: #F5F8FC;
}

.section__inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.display {
  display: flex;
  align-items: center;
  gap: 60px;
}

.display-md {
  display: none;
}

.text {
  flex: 1;
  max-width: 50%;
}

.image {
  flex: 1;
  max-width: 50%;
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 8px;
}

.detail {
  margin-bottom: 40px;
}

.title {
  font-size: 32px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 24px;
  line-height: 1.3;
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.new-tips {
  background-color: #FF4D4F;
  color: white;
  font-size: 12px;
  font-weight: normal;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
}

.desc {
  font-size: 16px;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.65);
}

.operation {
  margin-top: 32px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  border: 2px solid #334CD5;
  background-color: transparent;
  color: #334CD5;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn:hover {
  background-color: #334CD5;
  color: white;
}

.btn:hover .icon path {
  fill: white;
}

.icon {
  transition: all 0.2s ease-in-out;
}

/* 响应式设计 */
@media screen and (max-width: 960px) {
  .display {
    display: none;
  }
  
  .display-md {
    display: block;
  }
  
  .display-md .text {
    max-width: 100%;
    margin-bottom: 40px;
  }
  
  .display-md .image {
    max-width: 100%;
    order: -1;
    margin-bottom: 40px;
  }
  
  .section {
    padding: 60px 0;
  }
  
  .section__inner {
    padding: 0 16px;
  }
}

@media screen and (max-width: 720px) {
  .title {
    font-size: 24px;
    margin-bottom: 16px;
  }
  
  .desc {
    font-size: 14px;
  }
  
  .btn {
    font-size: 14px;
    padding: 10px 20px;
  }
  
  .section {
    padding: 40px 0;
  }
}

@media screen and (max-width: 480px) {
  .title {
    font-size: 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .new-tips {
    align-self: flex-start;
  }
}
</style>