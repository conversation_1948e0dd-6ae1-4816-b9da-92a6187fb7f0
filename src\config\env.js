// 环境配置
const env = import.meta.env.MODE || 'production'

const EnvConfig = {
  development: {
    baseApi: 'http://localhost:3000/api',
    mockApi: 'https://mock.apifox.cn/m1/3612114-0-default/api'
  },
  test: {
    baseApi: 'https://test-api.shijieu.cn/api',
    mockApi: 'https://mock.apifox.cn/m1/3612114-0-default/api'
  },
  production: {
    baseApi: 'https://api.shijieu.cn/api',
    mockApi: ''
  }
}

export default {
  env,
  mock: false,
  namespace: 'shijieu',
  ...EnvConfig[env]
} 