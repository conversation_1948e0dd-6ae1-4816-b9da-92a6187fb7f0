<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 表单数据
const formData = reactive({
  name: '',
  phone: '',
  email: '',
  school: '',
  position: '',
  message: '',
  type: '产品咨询'
})

// 表单规则
const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  school: [{ required: true, message: '请输入学校名称', trigger: 'blur' }],
  position: [{ required: true, message: '请输入职务', trigger: 'blur' }]
}

// 表单引用
const formRef = ref(null)

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      // 这里可以添加实际的表单提交逻辑
      console.log('表单数据:', formData)
      
      // 模拟提交成功
      ElMessage.success('提交成功，我们会尽快与您联系！')
      
      // 重置表单
      formRef.value.resetFields()
    } else {
      ElMessage.error('请正确填写表单信息')
      return false
    }
  })
}
</script>

<template>
  <div class="contact-page">
    <div class="container">
      <h1 class="page-title">联系我们</h1>
      
      <div class="contact-content">
        <div class="contact-info">
          <h2>联系方式</h2>
          
          <div class="info-item">
            <i class="info-icon phone-icon"></i>
            <div class="info-content">
              <h3>电话/微信</h3>
              <p>159 0100 6750 （9:00-18:00）</p>
            </div>
          </div>
          
          <div class="info-item">
            <i class="info-icon email-icon"></i>
            <div class="info-content">
              <h3>邮箱</h3>
              <p><EMAIL></p>
            </div>
          </div>
          
          <div class="info-item">
            <i class="info-icon address-icon"></i>
            <div class="info-content">
              <h3>地址</h3>
              <p>北京市朝阳区中国五矿大厦</p>
              <p>厦门市思明区新景中心</p>
              <p>深圳市福田区上步信托大楼</p>
            </div>
          </div>
          
          <div class="qrcode-box">
            <img src="@/assets/images/qrcode.jpg" alt="试界教育公众号" class="qrcode">
            <p>扫码关注公众号</p>
          </div>
        </div>
        
        <div class="contact-form">
          <h2>产品咨询</h2>
          <p class="form-desc">请填写以下信息，我们会尽快与您联系</p>
          
          <el-form 
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="80px"
            class="consultation-form"
          >
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" placeholder="请输入您的姓名"></el-input>
            </el-form-item>
            
            <el-form-item label="手机" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入您的手机号"></el-input>
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="formData.email" placeholder="请输入您的邮箱"></el-input>
            </el-form-item>
            
            <el-form-item label="学校" prop="school">
              <el-input v-model="formData.school" placeholder="请输入您的学校名称"></el-input>
            </el-form-item>
            
            <el-form-item label="职务" prop="position">
              <el-input v-model="formData.position" placeholder="请输入您的职务"></el-input>
            </el-form-item>
            
            <el-form-item label="咨询类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择咨询类型" style="width: 100%">
                <el-option label="产品咨询" value="产品咨询"></el-option>
                <el-option label="解决方案咨询" value="解决方案咨询"></el-option>
                <el-option label="合作洽谈" value="合作洽谈"></el-option>
                <el-option label="其他" value="其他"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="留言" prop="message">
              <el-input 
                v-model="formData.message" 
                type="textarea" 
                rows="4"
                placeholder="请输入您的咨询内容"
              ></el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="submitForm">提交</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.contact-page {
  padding: 120px 0 80px;
}

.page-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin-bottom: 40px;
  text-align: center;
}

.contact-content {
  display: flex;
  gap: 40px;
}

.contact-info {
  flex: 1;
  background-color: #f9f9f9;
  padding: 30px;
  border-radius: 8px;
}

.contact-info h2 {
  font-size: 24px;
  color: #334CD5;
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  margin-bottom: 25px;
}

.info-icon {
  width: 40px;
  height: 40px;
  background-color: #334CD5;
  border-radius: 50%;
  margin-right: 15px;
}

.info-content h3 {
  font-size: 18px;
  margin-bottom: 5px;
}

.info-content p {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
}

.qrcode-box {
  text-align: center;
  margin-top: 40px;
}

.qrcode {
  width: 150px;
  height: 150px;
  margin-bottom: 10px;
}

.contact-form {
  flex: 2;
}

.contact-form h2 {
  font-size: 24px;
  color: #334CD5;
  margin-bottom: 10px;
}

.form-desc {
  color: #666;
  margin-bottom: 30px;
}

.consultation-form {
  max-width: 600px;
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .contact-content {
    flex-direction: column;
  }
  
  .contact-info,
  .contact-form {
    width: 100%;
  }
}
</style> 