<template>
  <div class="product-page">
    <!-- 产品页面头部横幅 -->
    <ProductHeaderBanner />
    
    <!-- 职业生涯规划教育 -->
    <CareerEducationSection />
    
<!--    &lt;!&ndash; 心理健康教育 &ndash;&gt;-->
<!--    <MentalHealthSection />-->
<!--    -->
<!--    &lt;!&ndash; 学业指导 &ndash;&gt;-->
<!--    <AcademicGuidanceSection />-->
  </div>
</template>

<script setup>
import ProductHeaderBanner from '@/components/product/ProductHeaderBanner.vue'
import CareerEducationSection from '@/components/product/CareerEducationSection.vue'
// import MentalHealthSection from '@/components/product/MentalHealthSection.vue'
// import AcademicGuidanceSection from '@/components/product/AcademicGuidanceSection.vue'
</script>

<style scoped>
.product-page {
  min-height: 100vh;
}
</style>